# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Tech Stack & Architecture

- **Framework**: React 19 + React Router 7 (full-stack)
- **Build Tool**: Vite with Cloudflare Workers integration
- **Database**: Cloudflare D1 (SQLite) via Drizzle ORM
- **UI Library**: Tailwind CSS v4 + shadcn/ui components
- **Package Manager**: pnpm@10.13.1
- **Deployment**: Cloudflare Workers + Cloudflare Pages  
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **Code Quality**: Biome 2.1.3 for formatting and linting
- **UI Components**: Comprehensive Radix UI primitives collection (accordion, dialog, dropdown, etc.)
- **Icons**: Lucide React + Iconify with fluent-emoji, ri, simple-icons collections
- **Date**: date-fns for date manipulation + react-day-picker
- **HTTP**: Built-in fetch API
- **Form**: react-hook-form with zod resolvers and @hookform/resolvers
- **React hooks**: Custom hooks in `app/hooks/`
- **Animations**: GSAP and Motion for advanced animations
- **Email**: @react-email/components and @react-email/render for email templates
- **Charts**: Recharts for data visualization
- **Special Features**: FFmpeg.wasm for client-side video processing (optimized with dependency exclusions)
- **State Management**: React Context API (per Cursor rules - Zustand/Jotai for complex cases)
- **Carousel**: Embla Carousel React
- **Toast**: Sonner for notifications
- **Theming**: next-themes for dark/light mode
- **Utility Classes**: clsx, tailwind-merge, class-variance-authority for conditional styling

## Key Commands

```bash
# Development
pnpm dev                    # Start dev server with HMR (port 3000)
pnpm start                  # Start production worker locally via Wrangler
pnpm build                  # Build for production
pnpm preview               # Build and preview production locally

# Database
pnpm db:generate           # Generate migrations from schema changes
pnpm db:migrate            # Apply migrations to local D1
pnpm db:migrate-production # Apply migrations to production D1

# Type checking & code quality
pnpm typecheck            # Generate types and check TypeScript
pnpm cf-typegen           # Generate Cloudflare Worker types
pnpm format               # Format code with Biome
pnpm format:check         # Check formatting without writing
pnpm lint                 # Lint code with Biome
pnpm lint:fix             # Fix linting issues automatically
pnpm check                # Run both formatting and linting checks
pnpm check:fix            # Fix both formatting and linting issues

# Deployment
pnpm deploy               # Build and deploy to Cloudflare
npx wrangler versions upload    # Deploy preview URL
npx wrangler versions deploy    # Promote version to production
npx wrangler d1 create <name>   # Create new D1 database (first-time setup)
```

## Project Structure

```
app/                      # React Router app (main source)
  ├── routes/             # File-based routing (auto-generated via flatRoutes)
  ├── components/         # Reusable UI components
  │   ├── ui/             # shadcn/ui components (Radix UI primitives)
  │   └── [business]      # Business-specific components (author.tsx, post-list.tsx)
  ├── lib/               # Utilities and business logic
  ├── hooks/             # Custom React hooks
  └── routes.ts          # Route configuration (flatRoutes)

database/                # Database schema definitions
  └── schema.ts          # Drizzle ORM schema (users, posts, products tables)

drizzle/                 # Database migrations (auto-generated)
workers/                 # Cloudflare Workers entry point
  └── app.ts             # Main worker handler with D1 database context
public/                  # Static assets
```

## Architecture Patterns

### Full-Stack React Router 7
- **SSR Enabled**: Server-side rendering with `react-router.config.ts`
- **File-based Routing**: Uses `@react-router/fs-routes` with flat route structure
- **Route Types**: Auto-generated types in `+types/` directories for each route
- **Loaders & Actions**: Server-side data loading and mutations via route exports
- **Context Injection**: Database context injected via Cloudflare Workers binding

### Database Architecture
- **Schema**: Drizzle ORM with SQLite for D1 compatibility
- **Tables**: `users` (auth + admin), `posts` (blog content), `products` (product pages)
- **Migrations**: Auto-generated from schema changes, deployed via Wrangler
- **Context**: Database instance passed via AppLoadContext in workers/app.ts

### Component Architecture
- **shadcn/ui**: Comprehensive component library built on Radix UI primitives
- **Styling**: Tailwind CSS v4 with CSS variables for theming and oklch color space
- **CSS Configuration**: Uses `@plugin` directives for typography, animations, and iconify
- **Composition**: Components use compound patterns (e.g., Card.Header, Card.Content)
- **Custom Components**: Business logic components in `app/components/`

### Data Flow
1. **Client Request** → Cloudflare Worker (workers/app.ts)
2. **Worker** → React Router SSR with database context
3. **Route Loader** → Database query via Drizzle ORM
4. **Component** → Server-rendered HTML with hydration
5. **Client Hydration** → React takes over for interactivity

## Database Configuration

- **Production**: Cloudflare D1 database ID configured in `wrangler.jsonc`
- **Local**: Uses Wrangler's local D1 development
- **Schema**: SQLite tables for users, posts, and products
- **Migrations**: Managed via Drizzle Kit, auto-generated from schema changes

## Environment Variables

Required for production deployment:
- `CLOUDFLARE_ACCOUNT_ID` - Cloudflare account ID
- `CLOUDFLARE_TOKEN` - Cloudflare API token
- `GA_TRACKING_ID` - Google Analytics tracking ID (hardcoded: G-L5GCTXJJN0)

## Key Features & Integrations

- **Full-stack React** with server-side rendering
- **Cloudflare Workers** for edge deployment with Node.js compatibility
- **D1 Database** with Drizzle ORM and type-safe queries
- **R2 Storage** for file uploads (binding: R2_BUCKET)
- **FFmpeg.wasm** for client-side video processing (products/video-clipper)
- **shadcn/ui Components** with Radix UI primitives and Tailwind styling  
- **Email System** with @react-email/components for templated emails
- **Custom domain** configured (zhanghe.dev)
- **Admin interface** at `/admin` with user authentication
- **Product pages** under `/products/*` including video clipper tool with FFmpeg.wasm
- **Blog posts** under `/posts/*` with markdown support and dynamic routing
- **Product system**: Uses both database-driven and static product definitions in `route-utils.ts`

## Development Guidelines

### Code Style (Biome 2.1.3 Configuration)
- **Formatting**: 2-space indentation, double quotes, semicolons as needed
- **Line Width**: 80 characters maximum  
- **Import Style**: Use `import type` for type-only imports (enforced by Biome)
- **Dependencies**: Enable exhaustive dependency warnings for React hooks
- **File Coverage**: Applies to JS/TS/JSX/TSX/JSON/CSS files

### TypeScript Standards
- **Strict Mode**: Enabled with comprehensive type checking
- **Route Types**: Use auto-generated `+types/` imports for route data
- **Component Props**: Always type component props and state
- **Database Types**: Infer types from Drizzle schema exports
- **Strong Typing**: Use `unknown` instead of `any`, utilize generics and interfaces
- **Type Imports**: Use `import type` for type-only imports (enforced by linter)

### Component Patterns
- **File Naming**: 
  - Components: PascalCase (`MyComponent.tsx`)
  - Hooks: camelCase with `use` prefix (`useAuth.ts`)
  - Utils: camelCase (`dateUtils.ts`)
- **Imports**: React first, external libraries, then local imports with `~/` alias
- **Styling**: Tailwind utility classes with `cn()` helper for conditional classes
- **State Management**: Prefer React Context API; use Zustand/Jotai only for complex global state
- **Performance**: Use React.memo, useCallback, useMemo when necessary
- **Fragments**: Prefer `<>...</>` over unnecessary div wrappers

### React Router 7 Specific
- **Route Files**: Use dot notation for nested routes (`products.video-clipper.tsx`)
- **Data Loading**: Export `loader` functions for server-side data fetching
- **Mutations**: Export `action` functions for form submissions and mutations
- **Meta Tags**: Export `meta` functions for SEO and page metadata
- **Type Safety**: Import and use route-specific types from `+types/`
- **Type Generation**: Route types are auto-generated in `+types/` directories. If missing, run `npx @react-router/dev typegen` manually

### SSR & Hydration Considerations
- **Context Safety**: Check for provider existence in custom hooks
- **Client-Only Code**: Use `useEffect` for browser-only operations
- **Environment Checks**: Use `import.meta.env` for environment variables
- **CORS Headers**: Special headers configured for FFmpeg.wasm compatibility

### Database Development
- **Schema Changes**: Always run `pnpm db:generate` after schema modifications
- **Local Testing**: Use `pnpm db:migrate` to apply changes locally  
- **Production Deploy**: Use `pnpm db:migrate-production` for production changes
- **Type Safety**: Import schema types from `database/schema` exports
- **Configuration**: D1 database ID and R2 bucket configured in `wrangler.jsonc`

## Important File Patterns

### Route Organization
- **Flat Routes**: Uses dot notation (`products.video-clipper.tsx` for `/products/video-clipper`)
- **Index Routes**: `_index.tsx` files serve as default route handlers
- **Layout Routes**: Parent route files (`products.tsx`) provide shared layouts
- **API Routes**: `api.*.tsx` files handle API endpoints (e.g., `api.login.tsx`)
- **Product Routes**: Each product has its own route file with optimized meta tags and descriptions
- **Privacy Routes**: Products with privacy concerns have dedicated privacy pages (e.g., `products.native-translate.privacy.tsx`)

### Component Structure  
- **UI Components**: Located in `app/components/ui/` (shadcn/ui primitives)
- **Business Components**: Located in `app/components/` (author.tsx, post-list.tsx)
- **Type Imports**: Use `import type` for type-only imports per Biome rules
- **Composition**: Components use compound patterns (e.g., Card.Header, Card.Content)

## Environment & Dependencies

### Package Manager Configuration
- **Version**: pnpm@10.13.1 with specific built dependencies
- **Only Built Dependencies**: @tailwindcss/oxide, esbuild, sharp, workerd
- **Lock File**: pnpm-lock.yaml (version-controlled)

### Utility Patterns
- **Database Queries**: Centralized in `DatabaseQueries` class with error handling
- **Route Utilities**: `createMeta()` for consistent SEO meta tags across routes
- **Safe Loaders**: `createSafeLoader()` wrapper for error-resistant data loading
- **Product Management**: Static product definitions in `PRODUCTS` array for consistency
- **Type Safety**: Comprehensive TypeScript interfaces for all data structures

### Cloudflare Configuration  
- **Custom Domain**: zhanghe.dev configured in routes
- **D1 Database**: zhangheblog (ID: af33cc7b-139f-49e4-89fc-7a211804db7a)
- **R2 Bucket**: zhanghedev for file storage
- **Compatibility**: nodejs_compat flag enabled for Node.js APIs
- **Build Optimization**: FFmpeg dependencies optimized with `optimizeDeps` exclusions
- **CORS Headers**: Special headers for FFmpeg.wasm cross-origin compatibility
- **Chunk Size**: Increased warning limit for large assets (1000kB)

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.