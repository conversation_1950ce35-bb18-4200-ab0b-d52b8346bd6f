---
description:
globs:
alwaysApply: false
---
# My Frontend Development Rules for Cursor AI

## 1. Core Stack & Environment
- **Framework/Library**: React 19 (or latest stable).
- **Build Tool/Dev Server**: Vite.
- **Styling**: Tailwind CSS.
- **Package Manager**: pnpm.
- **Language**: **TypeScript**. All new code and examples should be in TypeScript. This includes:
    - Strong typing for props, state, function arguments, and return values.
    - Use of interfaces or types for defining data structures.
- **Project Structure**: Assume a standard Vite + React + TypeScript project structure:
    - `src/` for all source code.
    - `src/components/` for reusable UI components (e.g., `MyComponent.tsx`).
    - `src/pages/` or `src/views/` for page-level components (e.g., `HomePage.tsx`).
    - `src/hooks/` for custom React Hooks (e.g., `useAuth.ts`).
    - `src/utils/` for utility functions (e.g., `dateUtils.ts`).
    - `src/types/` or `src/interfaces/` for shared type definitions (e.g., `user.types.ts`).
    - `src/assets/` for static assets like images, fonts.
    - `public/` for static assets that are not processed by Vite.

## 2. React 19 Specifics
- **Component Style**: Prioritize functional components with Hooks. Avoid class components. Ensure all props and state are properly typed.
    - Example: `interface MyComponentProps { title: string; count?: number; }`
- **React 19 Features**: When applicable, suggest and use new React 19 features like:
    - Actions (for form submissions, mutations), with typed payloads and responses.
    - `useOptimistic` for optimistic UI updates, with typed data.
    - `useFormStatus` for form pending states.
    - `use` Hook for integrating promises or context, ensuring type safety.
    - Server Components: While I am primarily frontend, if a solution naturally lends itself to RSC concepts or if I ask about full-stack React, provide context and typed examples.
- **State Management**:
    - For local component state: `useState<MyStateType>()`, `useReducer<MyReducerType>()`.
    - For cross-component state: React Context API is preferred for simpler cases, with typed context values.
    - If more complex global state is needed, I might be using Zustand or Jotai (you can ask or I will specify). Assume Context API by default unless the complexity clearly warrants something else. Provide typed examples.
- **JSX**:
    - Write clean, readable JSX.
    - Use implicit returns for simple single-line JSX in arrow functions.
    - Fragments (`<>...</>`) are preferred over unnecessary `div` wrappers.

## 3. Vite Specifics
- **Configuration**: Refer to `vite.config.ts` for build configurations.
- **Environment Variables**: Use `import.meta.env.VITE_YOUR_VARIABLE_NAME` for environment variables. Define types for environment variables if possible (e.g., via a `vite-env.d.ts` file).
- **Plugins**: If suggesting Vite plugins, ensure they are compatible, commonly used, and ideally have good TypeScript support.

## 4. Tailwind CSS Specifics
- **Usage**: Apply utility classes directly in JSX.
- **Custom CSS**: Minimize custom CSS. If custom CSS is absolutely necessary, suggest placing it in a global CSS file (e.g., `src/index.css`) or a specific component's CSS module, and explain why it's needed.
- **Configuration**: Refer to `tailwind.config.js` (or `tailwind.config.ts` if I specify using it) for customizations.
- **Best Practices**:
    - Avoid excessive use of `@apply`. Prefer composing utilities directly in the JSX.
    - Keep class strings readable. Consider tools like `clsx` or `cva` (class-variance-authority, which is great with TypeScript) for conditional or complex class combinations if I ask for it or if it significantly improves readability.
    - Use Tailwind's responsive prefixes (e.g., `sm:`, `md:`, `lg:`) for responsive design.
    - Leverage JIT (Just-In-Time) mode features for arbitrary values if needed (e.g., `w-[300px]`).

## 5. pnpm Specifics
- **Commands**:
    - For installing packages: `pnpm add <package-name>`
    - For installing dev dependencies (including types like `@types/node`): `pnpm add -D <package-name> @types/<package-name-if-needed>`
    - For running scripts: `pnpm <script-name>` (e.g., `pnpm dev`, `pnpm build`)
- **Lockfile**: Be aware that projects use `pnpm-lock.yaml`.

## 6. General Coding Style & Preferences
- **Modern TypeScript**: Utilize ES6+ features along with strong TypeScript typing. Use `unknown` instead of `any` where possible for better type safety.
- **Readability**: Prioritize clear, concise, and maintainable code.
- **Comments**: Add JSDoc-style comments for functions, interfaces, and types, especially for public APIs. Add inline comments for complex logic or non-obvious decisions.
- **Error Handling**: Implement robust error handling (e.g., `try...catch` for async operations, Error Boundaries in React). Ensure error types are handled.
- **File Naming**:
    - Components: PascalCase (e.g., `MyComponent.tsx`, `UserProfile.tsx`).
    - Hooks: camelCase with `use` prefix (e.g., `useAuth.ts`, `useFormInput.ts`).
    - Utility files: camelCase (e.g., `dateUtils.ts`).
    - Type definition files: (e.g., `user.types.ts`, `api.interfaces.ts`).
- **Imports**:
    - Organize imports: React imports first, then external libraries, then local project imports (types, components, utils, etc.).
    - Use absolute imports configured via `tsconfig.json`'s `paths` option (e.g., `import MyComponent from '@/components/MyComponent'`). Assume this setup if feasible for new projects.
- **Performance**: Be mindful of performance implications (e.g., memoization with `React.memo`, `useCallback`, `useMemo` when necessary, code splitting).

## 7. What to Avoid
- **`any` type**: Avoid using `any` as much as possible. Prefer specific types, `unknown`, or generics.
- **Outdated Practices**: Avoid suggesting jQuery, older React patterns (like `createClass`), or JavaScript-only solutions where TypeScript is clearly beneficial.
- **Over-Engineering**: Prefer simpler solutions unless complexity is explicitly required or justified.
- **Unnecessary Libraries**: Don't suggest new libraries for simple tasks that can be achieved with built-in APIs or existing stack, especially if they lack good TypeScript support.

## 8. When Providing Code
- **TypeScript First and Foremost**: All code snippets, components, functions, etc., must be in TypeScript with complete and accurate type definitions.
- **Completeness**: If generating a component or function, try to make it self-contained, including necessary type/interface definitions, or clearly indicate dependencies.
- **Context**: Briefly explain the reasoning behind the suggested code or approach, especially regarding type choices.

By following these rules, you'll help me write better, type-safe code faster. Thanks!
