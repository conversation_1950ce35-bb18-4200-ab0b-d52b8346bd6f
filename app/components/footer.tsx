import { NavLink } from "react-router"
import { SOCIAL } from "./author"

export default function Footer() {
  return (
    <footer className="container max-w-screen-md mx-auto px-8 py-16 flex gap-4 md:gap-8 justify-center">
      {SOCIAL.map(({ name, icon, url }) => (
        <NavLink
          to={url}
          target="_blank"
          title={name}
          key={name}
          viewTransition
        >
          <i className={`${icon} text-2xl inline-block`} />
        </NavLink>
      ))}
    </footer>
  )
}
