import { NavLink } from "react-router"
import type { Product } from "~/lib/route-utils"
import { Badge } from "~/components/ui/badge"

interface ProductCardProps {
  product: Product & {
    icon?: React.ReactNode
    badge?: string
    category?: string
  }
  className?: string
}

export function ProductCard({ product, className }: ProductCardProps) {
  const linkProps = product.external
    ? {
        to: product.href,
        target: "_blank",
        rel: "noreferrer",
      }
    : {
        to: product.href,
        viewTransition: true,
      }

  return (
    <NavLink
      {...linkProps}
      className={`group bg-card rounded-lg p-6 hover:bg-card/80 transition-all duration-200 hover:shadow-md hover:scale-[1.02] relative overflow-hidden ${className || ""}`}
    >
      {/* Icon */}
      {product.icon && (
        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center mb-4 text-primary">
          {product.icon}
        </div>
      )}
      
      {/* Badge */}
      {product.badge && (
        <Badge 
          variant={product.badge === "New" ? "default" : "secondary"} 
          className="absolute top-4 right-4 text-xs"
        >
          {product.badge}
        </Badge>
      )}
      
      {/* Content */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
          {product.title}
        </h3>
        <p className="text-sm text-muted-foreground leading-relaxed">
          {product.description}
        </p>
      </div>
      
      {/* External link indicator */}
      {product.external && (
        <div className="absolute top-4 left-4">
          <Badge variant="outline" className="text-xs">
            External
          </Badge>
        </div>
      )}
    </NavLink>
  )
}

interface ProductGridProps {
  products: Product[]
  columns?: "1" | "2" | "3"
  className?: string
}

export function ProductGrid({
  products,
  columns = "2",
  className,
}: ProductGridProps) {
  const gridClass = {
    "1": "grid-cols-1",
    "2": "grid-cols-1 md:grid-cols-2",
    "3": "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  }

  return (
    <div className={`grid ${gridClass[columns]} gap-4 ${className || ""}`}>
      {products.map((product) => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
