interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({
  title,
  description,
  children,
  className,
}: PageHeaderProps) {
  return (
    <div
      className={
        className ||
        "container max-w-screen-md mx-auto min-h-[256px] p-8 gap-6 flex flex-col justify-end"
      }
    >
      <h1 className="text-5xl font-bold">{title}</h1>
      {description && <p className="opacity-80">{description}</p>}
      {children}
    </div>
  )
}

interface SectionHeaderProps {
  title: string
  viewAllLink?: {
    href: string
    label?: string
  }
  icon?: React.ReactNode
  className?: string
}

export function SectionHeader({
  title,
  viewAllLink,
  icon,
  className,
}: SectionHeaderProps) {
  return (
    <h2
      className={`mb-4 text-lg font-bold flex justify-between items-center ${className || ""}`}
    >
      <p className="flex items-center gap-1">
        {icon}
        {title}
      </p>
      {viewAllLink && (
        <a
          href={viewAllLink.href}
          className="text-sm opacity-80 hover:opacity-100 transition-opacity"
        >
          {viewAllLink.label || "View All"}
        </a>
      )}
    </h2>
  )
}
