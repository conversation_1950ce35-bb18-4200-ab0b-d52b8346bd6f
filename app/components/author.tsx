import { Link } from "react-router"

export const SOCIAL = [
  {
    name: "Github",
    icon: "icon-[ri--github-fill]",
    url: "https://github.com/zh30",
  },
  {
    name: "LinkedIn",
    icon: "icon-[ri--linkedin-box-fill]",
    url: "https://www.linkedin.com/in/zhanghe/",
  },
  {
    name: "Twitter | X",
    icon: "icon-[ri--twitter-x-line]",
    url: "https://twitter.com/zhanghedev",
  },
  {
    name: "Youtube",
    icon: "icon-[ri--youtube-fill]",
    url: "https://www.youtube.com/@zhanghedev",
  },
  {
    name: "Farcaster",
    icon: "icon-[simple-icons--farcaster]",
    url: "https://farcaster.xyz/zhanghe.eth",
  },
  {
    name: "Spotify",
    icon: "icon-[ri--spotify-fill]",
    url: "https://open.spotify.com/artist/7eLydZiButeEMvcbvVDzre?si=ZtC4hkENTPi1hIDxgG4cmg",
  },
  // {
  //   name: "BlueSky",
  //   icon: "icon-[ri--bluesky-fill]",
  //   url: "https://bsky.app/profile/zhanghe.dev"
  // },
  // {
  //   name: "Mastodon",
  //   icon: "icon-[ri--mastodon-fill]",
  //   url: "https://me.dm/@zhanghe",
  //   rel: "me"
  // },
  // {
  //   name: "Telegram",
  //   icon: "icon-[ri--telegram-fill]",
  //   url: "https://t.me/zhanghe_dev"
  // },
  // {
  //   name: "Podcast | 小宇宙",
  //   icon: "icon-[simple-icons--applepodcasts]",
  //   url: "https://www.xiaoyuzhoufm.com/podcast/616ce832bbbe908cc645b51f"
  // },
  {
    name: "Bilibili",
    icon: "icon-[ri--bilibili-fill]",
    url: "https://space.bilibili.com/35156867",
  },
  {
    name: "Email",
    icon: "icon-[ri--mail-send-fill]",
    url: "mailto:<EMAIL>",
  },
  // {
  //   name: "Sponsor",
  //   icon: "icon-[ri--hand-coin-line]",
  //   url: "https://app.ens.domains/zhanghe.eth"
  // },
]

type Props = {
  name?: string
  avatar?: string
  size?: "medium" | "large"
}
const Author = ({
  name = "Henry Zhang",
  avatar = "https://assets.zhanghe.dev/android-chrome-512x512.png",
  size = "medium",
}: Props) => (
  <section
    className={`inline-flex flex-row items-center ${size === "large" ? "gap-4" : "gap-2"}`}
    style={{
      viewTransitionName: "blog-author",
    }}
  >
    <Link to="/" viewTransition>
      <img
        src={avatar}
        alt={name}
        width={200}
        height={200}
        className={`rounded-full ${size === "large" ? "w-14 h-14" : "w-12 h-12"} border-2 border-gray-200 dark:border-gray-800`}
      />
    </Link>
    <div className={`flex flex-col ${size === "large" ? "gap-1.5" : "gap-1"}`}>
      <p className="">{name}</p>
      <p className="flex gap-3 flex-wrap leading-5">
        {SOCIAL.map(({ name, icon, url }) => (
          <Link to={url} target="_blank" title={name} key={name} viewTransition>
            <i className={`${icon} text-xl inline-block`} />
          </Link>
        ))}
      </p>
    </div>
  </section>
)

export default Author
