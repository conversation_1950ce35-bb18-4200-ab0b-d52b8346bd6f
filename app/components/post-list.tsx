import { NavLink } from "react-router"
import type { Post } from "~/database/schema"

export default function PostList({ posts }: { posts: Post[] }) {
  return (
    <div className="space-y-3">
      {posts.length > 0 ? (
        posts.map(({ title, id }: Post) => (
          <div className="text-3xl leading-snug" key={id}>
            <NavLink
              viewTransition
              to={`/posts/${id}`}
              className="relative before:absolute before:w-full before:h-1 before:rounded-sm before:bg-black dark:before:bg-white before:-bottom-1 before:left-0 before:origin-right before:transition-transform before:duration-300 before:ease-in-out before:scale-x-0 hover:before:origin-left hover:before:scale-x-100 select-none"
              style={({ isTransitioning }) => ({
                viewTransitionName: isTransitioning ? "blog-title" : "",
              })}
            >
              {title}
            </NavLink>
          </div>
        ))
      ) : (
        <div className="text-center text-muted-foreground mt-10">
          No posts found
        </div>
      )}
    </div>
  )
}
