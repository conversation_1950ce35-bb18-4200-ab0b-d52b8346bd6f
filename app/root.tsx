import { useEffect } from "react"
import ReactGA from "react-ga4"
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLocation,
} from "react-router"
import type { Route } from "./+types/root"
import { GA_TRACKING_ID } from "./lib/utils"
import "./app.css"
import Footer from "./components/footer"
import DotGrid from "./components/ui/dot-grid"

export const links: Route.LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
  { rel: "icon", href: "/favicon.ico" },
]

export function Layout({ children }: { children: React.ReactNode }) {
  const location = useLocation()

  useEffect(() => {
    try {
      ReactGA.initialize(GA_TRACKING_ID)
      ReactGA.send({
        hitType: "pageview",
        page: location.pathname,
      })
    } catch (error) {
      console.warn("Failed to initialize Google Analytics:", error)
    }
  }, [location])

  return (
    <html lang="en" className="dark">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="relative">
        <main className="relative z-10 min-h-screen flex flex-col">
          {children}
        </main>
        <DotGrid
          dotSize={4}
          gap={16}
          baseColor="#271e37"
          activeColor="#5227ff"
          proximity={100}
          shockRadius={250}
          shockStrength={5}
          resistance={750}
          returnDuration={1.5}
          className="absolute left-0 top-0 right-0 bottom-0 z-0"
        />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  return (
    <>
      <div className="flex-1 relative">
        <Outlet />
      </div>
      <Footer />
    </>
  )
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = "Oops!"
  let details = "An unexpected error occurred."
  let stack: string | undefined

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error"
    details =
      error.status === 404
        ? "The requested page could not be found."
        : error.statusText || details
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message
    stack = error.stack
  }

  return (
    <main className="pt-16 p-4 container mx-auto max-w-screen-md flex-1">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full p-4 overflow-x-auto">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  )
}
