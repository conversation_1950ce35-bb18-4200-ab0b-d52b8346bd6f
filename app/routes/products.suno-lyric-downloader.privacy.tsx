import { <PERSON> } from "react-router"
import { Shield, Lock, Globe, Mail, Calendar } from "lucide-react"
import { But<PERSON> } from "~/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title: "Privacy Policy - Suno Lyric Downloader Chrome Extension",
    description:
      "Comprehensive privacy policy for Suno Lyric Downloader Chrome extension. Learn about our offline operation, data protection, and commitment to user privacy. No data collection, no external servers.",
    keywords:
      "Suno lyric downloader privacy policy, Chrome extension privacy, data protection, offline browser extension, privacy commitment, no data collection, user privacy, secure extension, Suno.com privacy, lyric downloader privacy",
    ogUrl: "https://zhanghe.dev/products/suno-lyric-downloader/privacy",
    ogImage: "https://assets.zhanghe.dev/suno-lyric-downloader/og.jpg",
  })
}

export default function PrivacyPolicy() {
  return (
    <div className="container max-w-screen-xl mx-auto px-6 py-10 md:pt-20">
      {/* Header Section */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Shield className="w-8 h-8 text-blue-600" />
          <h1
            className="text-3xl md:text-5xl font-bold"
            style={{
              viewTransitionName: "privacy-title",
            }}
          >
            Privacy Policy
          </h1>
        </div>

        {/* Breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-4"
        >
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <a href="/products/suno-lyric-downloader" className="hover:opacity-80">
            Suno Lyric Downloader
          </a>
          <span className="mx-2">/</span>
          <span>Privacy Policy</span>
        </nav>

        <p className="text-xl text-muted-foreground font-light mb-8 max-w-3xl mx-auto">
          Your privacy is our priority. Learn how Suno Lyric Downloader protects your
          data with completely offline operation and zero data collection.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="/products/suno-lyric-downloader"
            className="cursor-pointer"
          >
            <Button variant="outline" size="lg">
              ← Back to Product
            </Button>
          </Link>
          <Link
            to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button className="cursor-pointer" size="lg">
              Get Extension
            </Button>
          </Link>
        </div>
      </div>

      {/* Privacy Commitment Card */}
      <Card className="mb-12 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Lock className="w-6 h-6 text-blue-600" />
            Our Privacy Commitment
          </CardTitle>
          <CardDescription className="text-lg">
            Suno Lyric Downloader is built with privacy as a core principle
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
                <Globe className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-semibold mb-2">100% Offline</h3>
              <p className="text-sm text-muted-foreground">
                All processing happens locally in your browser
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-semibold mb-2">No Data Collection</h3>
              <p className="text-sm text-muted-foreground">
                Zero telemetry, analytics, or user tracking
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-semibold mb-2">Open Source</h3>
              <p className="text-sm text-muted-foreground">
                Transparent code you can inspect yourself
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Privacy Policy Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-bold text-blue-600 dark:text-blue-400">
                1
              </div>
              Offline Operation & Data Processing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Suno Lyric Downloader operates completely offline with no external
                communication:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>No Data Collection:</strong> We don't collect any
                    personally identifiable information or usage data
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Local Processing:</strong> All lyric downloading and
                    processing happens on your local computer
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>No External Servers:</strong> The extension never
                    communicates with external servers to transmit data
                  </div>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center text-sm font-bold text-green-600 dark:text-green-400">
                2
              </div>
              Data Storage & Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Since this extension operates entirely offline:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>No Server Storage:</strong> No data is stored on our
                    servers
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Temporary Memory Only:</strong> Lyrics being processed
                    are only stored in your computer's memory
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Automatic Cleanup:</strong> All temporary data is
                    cleared when you close your browser or stop using the extension
                  </div>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center text-sm font-bold text-purple-600 dark:text-purple-400">
                3
              </div>
              Third-Party Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Our commitment to privacy extends to third-party integrations:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>No Third-Party Services:</strong> This extension does
                    not use any external services or APIs
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>No Data Sharing:</strong> Your data is never shared
                    with third parties
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Direct Suno Access:</strong> Only communicates with
                    Suno.com using your existing session
                  </div>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center text-sm font-bold text-orange-600 dark:text-orange-400">
                4
              </div>
              Browser Permissions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                We request minimal permissions necessary for core functionality:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Active Tab Access:</strong> Required to detect Suno
                    song pages and extract lyrics
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Script Injection:</strong> Used to add download
                    buttons to the Suno interface
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <strong>Storage:</strong> Saves your preferences locally in
                    the browser
                  </div>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Policy Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Since this extension doesn't collect data, privacy policy changes
                will be minimal:
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Changes only made for significant functionality updates</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>All updates posted on this page</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Last updated: September 2024</span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Have questions about our privacy practices?
              </p>
              <div className="space-y-3">
                <div>
                  <strong>Email:</strong>{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div>
                  <strong>GitHub:</strong>{' '}
                  <a
                    href="https://github.com/zh30/get-suno-lyric"
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    github.com/zh30/get-suno-lyric
                  </a>
                </div>
                <div>
                  <strong>Response Time:</strong> Typically within 24-48 hours
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Final CTA */}
      <Card className="text-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800">
        <CardContent className="pt-8">
          <div className="space-y-4">
            <h3 className="text-2xl font-bold">Download with Confidence</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Join thousands of users who trust Suno Lyric Downloader for secure,
              private lyric downloads. Your privacy is guaranteed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm"
                className="cursor-pointer"
                target="_blank"
                rel="noreferrer"
              >
                <Button className="cursor-pointer" size="lg">
                  Get Suno Lyric Downloader
                </Button>
              </Link>
              <Link
                to="https://github.com/zh30/get-suno-lyric"
                className="cursor-pointer"
                target="_blank"
                rel="noreferrer"
              >
                <Button variant="outline" size="lg">
                  View Source Code
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-800">
        <p className="text-muted-foreground">
          Thank you for choosing Suno Lyric Downloader and trusting us with your
          privacy.
        </p>
        <p className="text-sm text-muted-foreground mt-2">
          Last updated: September 2024 | Version 1.0
        </p>
      </div>
    </div>
  )
}
