import {
  <PERSON><PERSON>,
  CheckCircle2,
  Cpu,
  Download,
  GitCommit,
  Globe,
  Shield,
  Terminal,
} from "lucide-react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title:
      "Git Commit Analyzer - AI-Powered Git Commit Message Generator | Henry Zhang",
    description:
      "Free AI-powered Git plugin that automatically generates meaningful commit messages using Ollama. Supports Git Flow format, multiple languages, and cross-platform compatibility. Boost your development workflow with intelligent commit analysis.",
    keywords:
      "git commit analyzer, AI commit messages, Ollama git plugin, automatic commit messages, Git Flow, git automation, developer tools, git productivity, commit message generator, git workflow optimization, AI git assistant, open source git tools",
    ogUrl: "https://zhanghe.dev/products/git-commit-analyzer",
    ogImage: "https://zhanghe.dev/git-commit-analyzer-og.jpg",
  })
}

export default function GitCommitAnalyzer() {
  return (
    <>
      {/* 主要内容区域 - 添加语义化标签 */}
      <header className="container max-w-screen-md mx-auto min-h-[256px] p-8 gap-6 flex flex-col justify-end">
        <h1 className="text-5xl font-bold">Git Commit Analyzer</h1>
        <p className="opacity-80">
          AI-powered Git plugin that automatically generates meaningful commit
          messages using Ollama
        </p>
        {/* Add breadcrumb navigation for SEO */}
        <nav aria-label="Breadcrumb navigation" className="text-sm opacity-60">
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <span>Git Commit Analyzer</span>
        </nav>
      </header>

      <main className="container max-w-screen-lg mx-auto p-8">
        {/* 添加文章/内容结构 */}
        <article>
          {/* 工具介绍部分 */}
          <section className="mb-8">
            <h2 className="sr-only">Tool Introduction</h2>
            <p className="text-lg text-muted-foreground mb-4">
              Git Commit Analyzer is a powerful Git plugin that leverages AI to
              automatically generate meaningful commit messages based on your
              staged changes. It uses Ollama to analyze git diffs and propose
              commit messages following the Git Flow format, making your
              development workflow more efficient and consistent.
            </p>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">🤖 AI-Powered</h3>
                <p className="text-sm text-muted-foreground">
                  Uses Ollama for local AI processing
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">📝 Git Flow</h3>
                <p className="text-sm text-muted-foreground">
                  Follows Git Flow commit format
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">🌍 Multi-Language</h3>
                <p className="text-sm text-muted-foreground">
                  English and Chinese support
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">🔒 Private</h3>
                <p className="text-sm text-muted-foreground">
                  Local processing, no cloud uploads
                </p>
              </div>
            </div>
          </section>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* 左侧：功能展示 */}
            <section className="space-y-6">
              <header className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">Key Features</h2>
              </header>

              <div className="bg-card rounded-lg p-6 space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      Smart Message Generation
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Analyzes your staged changes and generates contextual,
                      meaningful commit messages that follow best practices.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                    <GitCommit className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Git Flow Compliant</h3>
                    <p className="text-sm text-muted-foreground">
                      Generates commit messages that follow Git Flow
                      conventions, making your repository history clean and
                      maintainable.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Terminal className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Interactive Mode</h3>
                    <p className="text-sm text-muted-foreground">
                      Choose to use, edit, or cancel the proposed commit
                      message, giving you full control over your commits.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Globe className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Cross-Platform</h3>
                    <p className="text-sm text-muted-foreground">
                      Works seamlessly on Linux, macOS, and Windows with
                      consistent behavior across all platforms.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* 右侧：安装和使用说明 */}
            <aside className="space-y-6">
              {/* 快速安装 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  Quick Installation
                </h3>

                <div className="space-y-4">
                  <div className="bg-muted rounded-lg p-4">
                    <h4 className="font-medium mb-2">🚀 One-Click Install</h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      The fastest way to install with a single command:
                    </p>
                    <div className="bg-background rounded p-3 font-mono text-sm">
                      bash -c "$(curl -fsSL
                      https://sh.zhanghe.dev/install-git-ca.sh)"
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Alternative Installation</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        <span>
                          Homebrew:{" "}
                          <code className="bg-muted px-1 rounded">
                            brew install zh30/tap/git-ca
                          </code>
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-600" />
                        <span>Manual build from source with Rust</span>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* 系统要求 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <Cpu className="w-5 h-5" />
                  System Requirements
                </h3>

                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span>Git (version 2.0 or later)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span>Ollama installed and running</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span>At least one language model in Ollama</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span>Linux, macOS, or Windows</span>
                  </div>
                </div>
              </section>

              {/* 使用方法 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <Terminal className="w-5 h-5" />
                  How to Use
                </h3>

                <div className="space-y-3 text-sm">
                  <div className="flex gap-3">
                    <span className="font-mono bg-muted px-2 py-1 rounded text-xs">
                      1
                    </span>
                    <span>
                      Stage your changes:{" "}
                      <code className="bg-muted px-1 rounded">git add</code>
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <span className="font-mono bg-muted px-2 py-1 rounded text-xs">
                      2
                    </span>
                    <span>
                      Run the analyzer:{" "}
                      <code className="bg-muted px-1 rounded">git ca</code>
                    </span>
                  </div>
                  <div className="flex gap-3">
                    <span className="font-mono bg-muted px-2 py-1 rounded text-xs">
                      3
                    </span>
                    <span>
                      Choose to use, edit, or cancel the suggested message
                    </span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <h4 className="font-medium text-sm mb-2">
                    Configuration Commands
                  </h4>
                  <div className="space-y-1 text-xs">
                    <div>
                      <code className="bg-background px-1 rounded">
                        git ca model
                      </code>{" "}
                      - Change AI model
                    </div>
                    <div>
                      <code className="bg-background px-1 rounded">
                        git ca language
                      </code>{" "}
                      - Set output language
                    </div>
                  </div>
                </div>
              </section>

              {/* 开源地址 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <GitCommit className="w-5 h-5" />
                  Open Source
                </h3>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Git Commit Analyzer is completely open source and available
                    on GitHub. Contributions are welcome!
                  </p>
                  <Button asChild variant="outline" className="w-full">
                    <a
                      href="https://github.com/zh30/git-commit-analyzer"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      <GitCommit className="w-4 h-4" />
                      View on GitHub
                    </a>
                  </Button>
                </div>
              </section>

              {/* 安全特性 */}
              <section className="bg-muted rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3 flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Privacy & Security
                </h3>
                <div className="space-y-2 text-sm opacity-80">
                  <p>
                    • <strong>Local Processing</strong>: All AI analysis happens
                    locally on your machine
                  </p>
                  <p>
                    • <strong>No Cloud Dependencies</strong>: Your code never
                    leaves your device
                  </p>
                  <p>
                    • <strong>Open Source</strong>: Fully transparent and
                    auditable codebase
                  </p>
                  <p>
                    • <strong>No Telemetry</strong>: We don't collect any usage
                    data
                  </p>
                </div>
              </section>
            </aside>
          </div>

          {/* 为什么选择 Git Commit Analyzer */}
          <section className="mt-12 bg-card rounded-lg p-8">
            <h3 className="text-2xl font-bold mb-6 text-center">
              Why Choose Git Commit Analyzer?
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center space-y-3">
                <div className="w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                  <Bot className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="font-semibold">AI-Powered Intelligence</h4>
                <p className="text-sm text-muted-foreground">
                  Leverages advanced AI models to understand your code changes
                  and generate contextually appropriate commit messages.
                </p>
              </div>
              <div className="text-center space-y-3">
                <div className="w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                  <GitCommit className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="font-semibold">Consistent Commit History</h4>
                <p className="text-sm text-muted-foreground">
                  Follows Git Flow standards automatically, ensuring clean and
                  maintainable commit history across your projects.
                </p>
              </div>
              <div className="text-center space-y-3">
                <div className="w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                  <Shield className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h4 className="font-semibold">Developer Privacy</h4>
                <p className="text-sm text-muted-foreground">
                  Complete privacy with local processing. Your sensitive code
                  never gets sent to external servers.
                </p>
              </div>
            </div>
          </section>
        </article>
      </main>
    </>
  )
}
