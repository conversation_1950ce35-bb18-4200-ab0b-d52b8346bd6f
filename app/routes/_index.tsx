import { Outlet } from "react-router"
import Author from "~/components/author"
import { SectionHeader } from "~/components/page-layout"
import PostList from "~/components/post-list"
import { ProductGrid } from "~/components/product-grid"
import { DatabaseQueries } from "~/lib/database-utils"
import { createMeta, PRODUCTS } from "~/lib/route-utils"
import type { Route } from "./+types/_index"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "<PERSON> Zhang - Web Developer & AI Tools Creator | Indie Hacker",
    description:
      "Professional web developer and indie hacker creating innovative AI tools, Chrome extensions, and web applications. Explore free tools for content creators, developers, and digital marketers.",
    keywords:
      "web developer, indie hacker, AI tools, Chrome extensions, web applications, content creation tools, video editing, productivity tools, <PERSON>, developer blog, software development",
    ogUrl: "https://zhanghe.dev/",
  })
}

export async function loader({ context }: Route.LoaderArgs) {
  const posts = await DatabaseQueries.getAllPosts(context.db)
  return { posts }
}

export default function Index({ loaderData }: Route.ComponentProps) {
  const { posts } = loaderData
  const featuredProducts = PRODUCTS.slice(0, 6) // Show first 6 products

  return (
    <>
      <div className="relative container mx-auto max-w-screen-md min-h-screen md:min-h-[512px] p-8 flex justify-center md:justify-between md:items-center flex-col gap-8 md:flex-row">
        <section>
          <h1 className="text-6xl font-bold mb-2">
            Ideation ~<br />& Action
          </h1>
          <p className="opacity-80">Web Developer</p>
        </section>
        <Author size="large" />
        <div className="md:hidden absolute left-1/2 bottom-4 -translate-x-1/2">
          <i className="icon-[ri--arrow-down-s-line] opacity-80 text-2xl inline-block animate-bounce" />
        </div>
      </div>

      <section className="container max-w-screen-md p-8 mx-auto">
        <SectionHeader
          title="Products"
          icon={
            <i className="mr-1 inline-block icon-[ri--apps-2-ai-line] align-text-bottom text-xl" />
          }
          viewAllLink={{ href: "/products" }}
        />
        <ProductGrid products={featuredProducts} />
      </section>

      <section className="container max-w-screen-md p-8 mx-auto">
        <SectionHeader
          title="Recent Posts"
          icon={
            <i className="mr-1 inline-block icon-[ri--quill-pen-line] align-text-bottom text-xl" />
          }
          viewAllLink={{ href: "/posts" }}
        />
        <PostList posts={posts} />
      </section>

      <Outlet />
    </>
  )
}
