import {
  AlertCircle,
  Calendar,
  CheckCircle,
  ChevronRight,
  Download,
  Eye,
  Globe,
  Info,
  Lock,
  Shield,
} from "lucide-react"
import { <PERSON> } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title:
      "Native Translate Privacy Policy - Local-First Translation with Zero Data Collection",
    description:
      "Privacy policy for Native Translate Chrome extension. Complete privacy-first approach with on-device translation, no cloud calls, no analytics, and zero data collection. Your data never leaves your browser.",
    keywords:
      "privacy policy, Native Translate, Chrome extension privacy, on-device translation, zero data collection, privacy-first, local translation, browser extension privacy, no analytics, secure translation, data protection, privacy commitment",
    ogUrl: "https://zhanghe.dev/products/native-translate/privacy",
    ogImage: "https://assets.zhanghe.dev/native-translate/og.jpg",
  })
}

export default function PrivacyPolicy() {
  return (
    <div className="container max-w-4xl mx-auto px-6 py-10 md:pt-20">
      {/* Header Section */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Shield className="w-8 h-8 text-green-600" />
          <h1
            className="text-3xl md:text-4xl font-bold"
            style={{
              viewTransitionName: "privacy-title",
            }}
          >
            Privacy Policy
          </h1>
        </div>

        {/* Breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-4"
        >
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <a href="/products/native-translate" className="hover:opacity-80">
            Native Translate
          </a>
          <span className="mx-2">/</span>
          <span>Privacy Policy</span>
        </nav>

        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-6">
          <Calendar className="w-4 h-4" />
          <span>Effective Date: August 14, 2025</span>
        </div>

        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Native Translate is built with privacy as the foundation. Our
          local-first approach ensures your data never leaves your device. No
          cloud calls, no analytics, no tracking— just pure, private
          translation.
        </p>
      </div>

      {/* Quick Navigation */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            Quick Navigation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <a
              href="#scope"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              Scope
            </a>
            <a
              href="#information"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              Information We Process
            </a>
            <a
              href="#no-collect"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              What We Don't Collect
            </a>
            <a
              href="#how-works"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              How Translation Works
            </a>
            <a
              href="#permissions"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              Permissions
            </a>
            <a
              href="#data-retention"
              className="flex items-center gap-2 text-sm hover:text-blue-600 transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
              Data Retention
            </a>
          </div>
        </CardContent>
      </Card>

      {/* Privacy Commitment Card */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-8 mb-8">
        <div className="flex items-center gap-3 mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
          <h2 className="text-2xl font-bold">Our Privacy Commitment</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-start gap-3">
            <Lock className="w-5 h-5 text-green-600 mt-1" />
            <div>
              <h3 className="font-semibold mb-1">Zero Data Collection</h3>
              <p className="text-sm text-muted-foreground">
                No analytics, no telemetry, no external servers. Your data stays
                on your device.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <Globe className="w-5 h-5 text-green-600 mt-1" />
            <div>
              <h3 className="font-semibold mb-1">Local Processing</h3>
              <p className="text-sm text-muted-foreground">
                All translation happens using Chrome's built-in on-device AI
                capabilities.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <Eye className="w-5 h-5 text-green-600 mt-1" />
            <div>
              <h3 className="font-semibold mb-1">Complete Transparency</h3>
              <p className="text-sm text-muted-foreground">
                Open-source code with clear explanations of all data processing.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-8">
        {/* Introduction */}
        <Card id="scope">
          <CardHeader>
            <CardTitle>Scope & Introduction</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>
              This Privacy Policy explains how the Native Translate browser
              extension processes information. We designed Native Translate to
              be privacy-first: translation and language detection run on your
              device using Chrome's built-in on-device AI. By using the
              Extension, you agree to this Policy.
            </p>
            <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
              <p className="text-sm">
                <strong>Important:</strong> This Policy applies solely to the
                Native Translate Chrome extension. It does not cover Chrome, the
                Chrome Web Store, your operating system, or any websites you
                visit.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Information We Process */}
        <Card id="information">
          <CardHeader>
            <CardTitle>Information We Process</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Download className="w-4 h-4" />
                Extension Settings (Stored Locally)
              </h3>
              <p className="text-muted-foreground mb-3">
                We store minimal preferences in Chrome storage, such as:
              </p>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm space-y-2">
                <div>
                  <code className="bg-background px-2 py-1 rounded">
                    nativeTranslate.settings
                  </code>{" "}
                  - target language, hotkey modifier
                </div>
                <div>
                  <code className="bg-background px-2 py-1 rounded">
                    nativeTranslate:readyPairs
                  </code>{" "}
                  - language-pair readiness timestamps
                </div>
                <div>
                  <code className="bg-background px-2 py-1 rounded">
                    nativeTranslate:detectorReady
                  </code>{" "}
                  - language detector readiness timestamp
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-3">
                These values stay on your device and are never transmitted to
                us.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Globe className="w-4 h-4" />
                On-Page Text for Translation
              </h3>
              <p className="text-muted-foreground">
                When you request translation, the Extension reads visible text
                nodes on the current page to generate translations. It avoids
                editing contexts and sensitive elements where feasible (e.g.,
                input, textarea, code, pre, navigation, headers/footers). Text
                is processed in memory and not saved or transmitted.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Keyboard Modifier State
              </h3>
              <p className="text-muted-foreground">
                To support hover-to-translate, the Extension listens for
                Alt/Control/Shift to detect activation. It does not record
                keystroke content and ignores typing contexts where feasible.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                Diagnostics
              </h3>
              <p className="text-muted-foreground">
                Limited console logs may be written to your local browser
                console for reliability. No analytics, telemetry, or remote
                logging are performed.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* What We Don't Collect */}
        <Card id="no-collect">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="w-5 h-5" />
              What We Do Not Collect
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No personal account data (names, emails, passwords)</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No browsing history beyond active tab context</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No unique advertising identifiers</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No server-side logs or external telemetry</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No usage analytics or behavior tracking</span>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>No third-party data sharing</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* How Translation Works */}
        <Card id="how-works">
          <CardHeader>
            <CardTitle>How Translation Works (Local-First)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold mt-0.5">
                1
              </div>
              <div>
                <h3 className="font-semibold mb-1">
                  On-Device Translation & Detection
                </h3>
                <p className="text-muted-foreground text-sm">
                  The Extension uses Chrome's built-in Translator and Language
                  Detector APIs to process text on your device. By default, no
                  cloud translation requests are made by the Extension.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold mt-0.5">
                2
              </div>
              <div>
                <h3 className="font-semibold mb-1">
                  Model Downloads Managed by Chrome
                </h3>
                <p className="text-muted-foreground text-sm">
                  On first use, Chrome may download on-device models and cache
                  them for offline use. These downloads are handled by Chrome,
                  not by us, and may be subject to Chrome/Google policies.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card id="permissions">
          <CardHeader>
            <CardTitle>Permissions We Request</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <code className="bg-background px-2 py-1 rounded text-sm">
                    storage
                  </code>
                  <span className="text-sm">
                    Save local settings and readiness metadata
                  </span>
                </div>
                <Shield className="w-4 h-4 text-green-600" />
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <code className="bg-background px-2 py-1 rounded text-sm">
                    activeTab / tabs
                  </code>
                  <span className="text-sm">
                    Interact with current tab for translation
                  </span>
                </div>
                <Shield className="w-4 h-4 text-green-600" />
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <code className="bg-background px-2 py-1 rounded text-sm">
                    scripting
                  </code>
                  <span className="text-sm">
                    Inject content script when needed
                  </span>
                </div>
                <Shield className="w-4 h-4 text-green-600" />
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <code className="bg-background px-2 py-1 rounded text-sm">
                    sidePanel
                  </code>
                  <span className="text-sm">
                    Provide optional side panel UI
                  </span>
                </div>
                <Shield className="w-4 h-4 text-green-600" />
              </div>

              <p className="text-sm text-muted-foreground mt-4">
                We request only the permissions necessary for core
                functionality.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Data Sharing */}
        <Card>
          <CardHeader>
            <CardTitle>Data Sharing and Transfers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold">No Third-Party Sharing</h3>
                  <p className="text-muted-foreground text-sm">
                    We do not sell, rent, or share your information with third
                    parties.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold">No Cross-Border Transfers</h3>
                  <p className="text-muted-foreground text-sm">
                    The Extension does not transmit your data to any external
                    servers.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Retention */}
        <Card id="data-retention">
          <CardHeader>
            <CardTitle>Data Retention</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Local Preferences</h3>
              <p className="text-muted-foreground text-sm">
                Remain in Chrome's local or session storage until you remove the
                Extension or clear site/extension data.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-2">In-Memory Caches</h3>
              <p className="text-muted-foreground text-sm">
                Translation results may be cached in memory during the session
                to improve performance and are discarded when the page or
                browser is closed.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-2">No Long-Term Storage</h3>
              <p className="text-muted-foreground text-sm">
                We do not persist on-page text or translation content beyond the
                current session.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Security */}
        <Card>
          <CardHeader>
            <CardTitle>Security</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start gap-3">
              <Lock className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold">Local Processing</h3>
                <p className="text-muted-foreground text-sm">
                  Reduces exposure by keeping translation on your device.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold">Open-Source Code</h3>
                <p className="text-muted-foreground text-sm">
                  The project is open source, allowing community review. As with
                  any software, no method is 100% secure, and you use the
                  Extension at your own risk.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Your Choices */}
        <Card>
          <CardHeader>
            <CardTitle>Your Choices</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold">Change or Remove Settings</h3>
                <p className="text-muted-foreground text-sm">
                  Adjust preferences in the popup or clear extension data via
                  your browser settings.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold">Disable or Uninstall</h3>
                <p className="text-muted-foreground text-sm">
                  You can disable or remove the Extension at any time from{" "}
                  <code className="bg-muted px-1 rounded">
                    chrome://extensions
                  </code>
                  .
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
              <div>
                <h3 className="font-semibold">Site Limitations</h3>
                <p className="text-muted-foreground text-sm">
                  Some pages (e.g.,{" "}
                  <code className="bg-muted px-1 rounded">chrome://</code>) do
                  not allow script injection; the Extension will not run there.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Children's Privacy */}
        <Card>
          <CardHeader>
            <CardTitle>Children's Privacy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              The Extension is not directed to children and does not knowingly
              collect personal information from children.
            </p>
          </CardContent>
        </Card>

        {/* Changes */}
        <Card>
          <CardHeader>
            <CardTitle>Changes to This Policy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              We may update this Policy from time to time. Material changes will
              be reflected by updating the Effective Date above. Your continued
              use after changes indicates acceptance.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Contact & Links */}
      <div className="mt-12 text-center space-y-4">
        <h2 className="text-2xl font-bold">Questions or Concerns?</h2>
        <p className="text-muted-foreground">
          If you have any questions about this Privacy Policy, please review our
          open-source code or reach out through our GitHub repository.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="https://github.com/zh30/native-translate"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button variant="outline" size="lg">
              <Globe className="w-4 h-4 mr-2" />
              View Source Code
            </Button>
          </Link>
          <Link to="/products/native-translate" className="cursor-pointer">
            <Button size="lg">Back to Native Translate</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
