import { <PERSON> } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products.mangoflow"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title:
      "MangoFlow - Free AI Chat Sidebar Chrome Extension | Smart Web Browsing Assistant",
    description:
      "Free AI-powered Chrome extension that adds an intelligent chat sidebar to your browser. Get instant answers, content summaries, and AI assistance while browsing. Perfect for research and productivity.",
    keywords:
      "AI chat sidebar, Chrome extension, web browsing assistant, AI web helper, smart internet surfing, browser AI, web content summarizer, productivity tools, AI assistant, browsing companion",
    ogImage: "https://assets.zhanghe.dev/mangoflow/og.jpg",
    ogUrl: "https://zhanghe.dev/products/mangoflow",
  })
}

export default function MangoFlow() {
  return (
    <div className="container max-w-screen-md mx-auto px-6 py-10 md:pt-20 text-center">
      <h1 className="text-3xl md:text-5xl font-bold inline-block mb-8">
        MangoFlow AI Chat Sidebar
      </h1>
      <p className="max-w-[750px] text-center text-lg text-muted-foreground sm:text-xl font-light opacity-80 mb-8">
        Your intelligent AI assistant that makes web browsing smarter, faster,
        and more productive. Get instant information without leaving your
        current page.
      </p>
      <Link
        to="https://chromewebstore.google.com/detail/mangoflow-ai-chat-sidebar/pfbbldnopphjpkfkalhajnkglnlacboa?hl=en&authuser=0"
        target="_blank"
        rel="noreferrer"
        className="cursor-pointer"
      >
        <Button className="cursor-pointer" size="lg">
          Get it for Free
        </Button>
      </Link>
    </div>
  )
}
