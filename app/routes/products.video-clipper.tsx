import type { FFmpeg } from "@ffmpeg/ffmpeg"
import {
  AlertCircle,
  CheckCircle2,
  Download,
  Scissors,
  Upload,
  Video,
} from "lucide-react"
import { useCallback, useEffect, useRef, useState } from "react"
import { Alert, AlertDescription } from "~/components/ui/alert"
import { But<PERSON> } from "~/components/ui/button"
import { Progress } from "~/components/ui/progress"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products.video-clipper"

export function meta() {
  return createMeta({
    title:
      "Free Online Video Clipper - Convert Long Videos to Short Clips | Browser-Based",
    description:
      "Free browser-based video clipper that converts long videos to short clips instantly. No upload required - powered by FFmpeg.wasm. Perfect for social media content creation, supports MP4, WebM, AVI formats.",
    keywords:
      "video clipper, video trimmer, short video maker, video converter, FFmpeg online, video editing, clip video, trim video, video cutter, free video tools, browser video editor, no upload video editor, social media video editor",
    ogUrl: "https://zhanghe.dev/products/video-clipper",
    ogImage: "https://zhanghe.dev/og-video-clipper.png",
  })
}

interface ProcessingState {
  isProcessing: boolean
  progress: number
  status: string
  error: string | null
}

interface VideoClipSettings {
  mode: "single" | "split"
  startTime: number
  endTime: number
  numSegments: number
  segmentDuration: number
  outputFormat: "mp4" | "webm"
  quality: "high" | "medium" | "low"
}

interface VideoSegment {
  index: number
  startTime: number
  endTime: number
  fileName: string
  blob?: Blob
  downloadUrl?: string
}

export default function VideoClipper(_: Route.ComponentProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [processing, setProcessing] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
    status: "",
    error: null,
  })
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null)
  const [processedSegments, setProcessedSegments] = useState<VideoSegment[]>([])
  const [videoDuration, setVideoDuration] = useState<number>(0)
  const [clipSettings, setClipSettings] = useState<VideoClipSettings>({
    mode: "split",
    startTime: 0,
    endTime: 30,
    numSegments: 3,
    segmentDuration: 30,
    outputFormat: "mp4",
    quality: "medium",
  })
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLButtonElement>(null)
  const ffmpegRef = useRef<FFmpeg | null>(null)
  const objectUrlsRef = useRef<string[]>([]) // Track all created URLs for cleanup

  // Request notification permission on mount and setup cleanup
  useEffect(() => {
    if ("Notification" in window && Notification.permission === "default") {
      Notification.requestPermission()
    }

    // Cleanup function to revoke all object URLs
    return () => {
      objectUrlsRef.current.forEach((url) => {
        try {
          URL.revokeObjectURL(url)
        } catch (e) {
          console.warn("Failed to revoke URL:", e)
        }
      })
      objectUrlsRef.current = []
    }
  }, [])

  const processSingleClip = useCallback(
    async (ffmpeg: FFmpeg, inputFileName: string) => {
      const outputFileName = `output.${clipSettings.outputFormat}`

      // Prepare FFmpeg command based on settings
      const duration = clipSettings.endTime - clipSettings.startTime
      if (duration <= 0) {
        throw new Error("End time must be greater than start time")
      }

      setProcessing((prev) => ({
        ...prev,
        status: "Processing video...",
        progress: 30,
      }))

      // Pre-calculate mimeType to avoid repeated checks
      const mimeType =
        clipSettings.outputFormat === "webm" ? "video/webm" : "video/mp4"

      // Build optimized FFmpeg command with performance flags
      const command = [
        "-i",
        inputFileName,
        "-ss",
        clipSettings.startTime.toString(),
        "-t",
        duration.toString(),
        "-avoid_negative_ts",
        "make_zero",
        "-threads",
        "0", // Use optimal threading
        "-movflags",
        "+faststart", // Optimize for web playback
      ]

      // Add quality settings with performance optimizations
      if (clipSettings.quality === "high") {
        command.push("-crf", "18", "-preset", "slow", "-tune", "fastdecode")
      } else if (clipSettings.quality === "medium") {
        command.push("-crf", "23", "-preset", "medium", "-tune", "fastdecode")
      } else {
        command.push("-crf", "28", "-preset", "fast", "-tune", "fastdecode")
      }

      // Add format-specific settings with optimizations
      if (clipSettings.outputFormat === "webm") {
        command.push("-c:v", "libvpx-vp9", "-c:a", "libopus", "-row-mt", "1")
      } else {
        command.push(
          "-c:v",
          "libx264",
          "-c:a",
          "aac",
          "-profile:v",
          "high",
          "-level",
          "4.0"
        )
      }

      command.push(outputFileName)

      // Execute FFmpeg command
      await ffmpeg.exec(command)

      setProcessing((prev) => ({
        ...prev,
        status: "Preparing download...",
        progress: 95,
      }))

      // Read the output file
      const outputData = await ffmpeg.readFile(outputFileName)

      if (!outputData || outputData.length <= 48) {
        throw new Error("Video processing failed - output file is empty")
      }

      // Create download URL
      const blob = new Blob([Buffer.from(outputData)], { type: mimeType })
      const url = URL.createObjectURL(blob)
      objectUrlsRef.current.push(url) // Track for cleanup
      setDownloadUrl(url)

      setProcessing((prev) => ({
        ...prev,
        status: "Complete!",
        progress: 100,
        isProcessing: false,
      }))

      // Show browser notification
      if ("Notification" in window && Notification.permission === "granted") {
        new Notification("Video Clipper - Processing Complete!", {
          body: `Your ${duration}s video clip is ready for download.`,
          icon: "/favicon.ico",
          tag: "video-complete",
        })
      }

      // Trigger automatic download
      const link = document.createElement("a")
      link.href = url
      link.download = `clip-${clipSettings.startTime}s-${clipSettings.endTime}s.${clipSettings.outputFormat}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Cleanup FFmpeg files
      try {
        await ffmpeg.deleteFile(inputFileName)
        await ffmpeg.deleteFile(outputFileName)
      } catch (e) {
        console.warn("Cleanup warning:", e)
      }
    },
    [clipSettings]
  )

  const processSplitVideo = useCallback(
    async (ffmpeg: FFmpeg, inputFileName: string) => {
      const segments: VideoSegment[] = []
      const totalDuration = videoDuration
      const segmentDuration = Math.floor(
        totalDuration / clipSettings.numSegments
      )

      setProcessing((prev) => ({
        ...prev,
        status: `Creating ${clipSettings.numSegments} video segments...`,
        progress: 30,
      }))

      // Calculate segments
      for (let i = 0; i < clipSettings.numSegments; i++) {
        const startTime = i * segmentDuration
        const endTime =
          i === clipSettings.numSegments - 1
            ? totalDuration
            : (i + 1) * segmentDuration
        const fileName = `segment_${i + 1}.${clipSettings.outputFormat}`

        segments.push({
          index: i + 1,
          startTime,
          endTime,
          fileName,
        })
      }

      // Pre-build command template to avoid repeated string operations
      const baseCommand = [
        "-i",
        inputFileName,
        "-avoid_negative_ts",
        "make_zero",
        "-threads",
        "0", // Use optimal threading
        "-movflags",
        "+faststart", // Optimize for web playback
      ]

      // Add quality settings once with performance optimizations
      if (clipSettings.quality === "high") {
        baseCommand.push("-crf", "18", "-preset", "slow", "-tune", "fastdecode")
      } else if (clipSettings.quality === "medium") {
        baseCommand.push(
          "-crf",
          "23",
          "-preset",
          "medium",
          "-tune",
          "fastdecode"
        )
      } else {
        baseCommand.push("-crf", "28", "-preset", "fast", "-tune", "fastdecode")
      }

      // Add format-specific settings once with optimizations
      if (clipSettings.outputFormat === "webm") {
        baseCommand.push(
          "-c:v",
          "libvpx-vp9",
          "-c:a",
          "libopus",
          "-row-mt",
          "1"
        )
      } else {
        baseCommand.push(
          "-c:v",
          "libx264",
          "-c:a",
          "aac",
          "-profile:v",
          "high",
          "-level",
          "4.0"
        )
      }

      // Process segments in smaller batches to avoid memory issues
      const batchSize = 3
      const mimeType =
        clipSettings.outputFormat === "webm" ? "video/webm" : "video/mp4"

      for (
        let batchStart = 0;
        batchStart < segments.length;
        batchStart += batchSize
      ) {
        const batchEnd = Math.min(batchStart + batchSize, segments.length)
        const batchPromises: Promise<void>[] = []

        for (let i = batchStart; i < batchEnd; i++) {
          const segment = segments[i]
          const progressBase = 30 + (i / segments.length) * 60

          setProcessing((prev) => ({
            ...prev,
            status: `Processing segment ${i + 1} of ${segments.length}...`,
            progress: Math.round(progressBase),
          }))

          // Create command for this segment by copying base command
          const command = [...baseCommand]
          command.push(
            "-ss",
            segment.startTime.toString(),
            "-t",
            (segment.endTime - segment.startTime).toString(),
            segment.fileName
          )

          // Process segment and return promise
          const processPromise = (async () => {
            await ffmpeg.exec(command)

            const segmentData = await ffmpeg.readFile(segment.fileName)

            if (!segmentData || segmentData.length <= 48) {
              throw new Error(
                `Segment ${i + 1} processing failed - output file is empty`
              )
            }

            const blob = new Blob([Buffer.from(segmentData)], {
              type: mimeType,
            })
            const downloadUrl = URL.createObjectURL(blob)
            objectUrlsRef.current.push(downloadUrl) // Track for cleanup

            segments[i] = {
              ...segment,
              blob,
              downloadUrl,
            }
          })()

          batchPromises.push(processPromise)
        }

        // Wait for current batch to complete
        await Promise.all(batchPromises)
      }

      setProcessing((prev) => ({
        ...prev,
        status: "Preparing downloads...",
        progress: 95,
      }))

      // Update state with processed segments
      setProcessedSegments(segments)

      setProcessing((prev) => ({
        ...prev,
        status: "Complete!",
        progress: 100,
        isProcessing: false,
      }))

      // Show browser notification
      if ("Notification" in window && Notification.permission === "granted") {
        new Notification("Video Clipper - Processing Complete!", {
          body: `Your video has been split into ${segments.length} short clips and is ready for download.`,
          icon: "/favicon.ico",
          tag: "video-complete",
        })
      }

      // Cleanup FFmpeg files
      try {
        await ffmpeg.deleteFile(inputFileName)
        for (const segment of segments) {
          await ffmpeg.deleteFile(segment.fileName)
        }
      } catch (e) {
        console.warn("Cleanup warning:", e)
      }
    },
    [clipSettings, videoDuration]
  )

  const initializeFFmpeg = useCallback(async () => {
    if (ffmpegRef.current && ffmpegLoaded) return ffmpegRef.current

    try {
      const { FFmpeg } = await import("@ffmpeg/ffmpeg")
      const { toBlobURL } = await import("@ffmpeg/util")

      const ffmpeg = new FFmpeg()

      // Set up event listeners for progress and logs
      ffmpeg.on("log", ({ message }) => {
        console.log("FFmpeg log:", message)
      })

      ffmpeg.on("progress", ({ progress }) => {
        const progressPercent = Math.round(progress * 100)
        setProcessing((prev) => ({
          ...prev,
          progress: Math.min(progressPercent, 95), // Cap at 95% until completion
          status: `Processing video... ${progressPercent}%`,
        }))
      })

      // Use faster CDN with caching headers
      const baseURL =
        "https://cdn.jsdelivr.net/npm/@ffmpeg/core-mt@0.12.6/dist/esm"

      try {
        // Try multi-threaded version first for better performance
        await ffmpeg.load({
          coreURL: await toBlobURL(
            `${baseURL}/ffmpeg-core.js`,
            "text/javascript"
          ),
          wasmURL: await toBlobURL(
            `${baseURL}/ffmpeg-core.wasm`,
            "application/wasm"
          ),
          workerURL: await toBlobURL(
            `${baseURL}/ffmpeg-core.worker.js`,
            "text/javascript"
          ),
        })
      } catch (mtError) {
        console.warn(
          "Multi-threaded FFmpeg failed, falling back to single-threaded:",
          mtError
        )
        // Fallback to single-threaded version
        const fallbackURL =
          "https://cdn.jsdelivr.net/npm/@ffmpeg/core@0.12.6/dist/esm"
        await ffmpeg.load({
          coreURL: await toBlobURL(
            `${fallbackURL}/ffmpeg-core.js`,
            "text/javascript"
          ),
          wasmURL: await toBlobURL(
            `${fallbackURL}/ffmpeg-core.wasm`,
            "application/wasm"
          ),
        })
      }

      ffmpegRef.current = ffmpeg
      setFFmpegLoaded(true)
      return ffmpeg
    } catch (error) {
      console.error("Failed to initialize FFmpeg:", error)
      throw new Error(
        `Failed to load video processing engine: ${error instanceof Error ? error.message : "Unknown error"}`
      )
    }
  }, [ffmpegLoaded])

  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith("video/")) {
      setProcessing((prev) => ({
        ...prev,
        error: "Please select a valid video file (MP4, WebM, AVI, MOV)",
      }))
      return
    }

    if (file.size > 1024 * 1024 * 1024) {
      // 1GB limit
      setProcessing((prev) => ({
        ...prev,
        error: "File size must be under 1GB for optimal performance",
      }))
      return
    }

    setSelectedFile(file)
    setProcessing((prev) => ({ ...prev, error: null }))

    // Clean up previous URLs
    setDownloadUrl((prevUrl) => {
      if (prevUrl) {
        URL.revokeObjectURL(prevUrl)
        objectUrlsRef.current = objectUrlsRef.current.filter(
          (url) => url !== prevUrl
        )
      }
      return null
    })

    setProcessedSegments((prevSegments) => {
      prevSegments.forEach((segment) => {
        if (segment.downloadUrl) {
          URL.revokeObjectURL(segment.downloadUrl)
          objectUrlsRef.current = objectUrlsRef.current.filter(
            (url) => url !== segment.downloadUrl
          )
        }
      })
      return []
    })

    // Create video element to get duration
    const video = document.createElement("video")
    video.preload = "metadata"
    video.onloadedmetadata = () => {
      const duration = Math.floor(video.duration)
      setVideoDuration(duration)

      // Calculate optimal segment duration for short videos (15-60s)
      const optimalDuration = duration > 180 ? 30 : duration > 120 ? 20 : 15
      const suggestedSegments = Math.max(
        2,
        Math.min(10, Math.floor(duration / optimalDuration))
      )

      setClipSettings((prev) => ({
        ...prev,
        endTime: Math.min(60, duration),
        numSegments: suggestedSegments,
        segmentDuration: optimalDuration,
      }))
      URL.revokeObjectURL(video.src)
    }
    video.src = URL.createObjectURL(file)
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragOver(false)
      const file = e.dataTransfer.files[0]
      if (file) handleFileSelect(file)
    },
    [handleFileSelect]
  )

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const processVideo = useCallback(async () => {
    if (!selectedFile) return

    setProcessing({
      isProcessing: true,
      progress: 0,
      status: "Initializing video processor...",
      error: null,
    })

    try {
      // Initialize FFmpeg
      setProcessing((prev) => ({
        ...prev,
        status: "Loading FFmpeg engine...",
        progress: 10,
      }))
      const ffmpeg = await initializeFFmpeg()

      const { fetchFile } = await import("@ffmpeg/util")

      // Load video file
      setProcessing((prev) => ({
        ...prev,
        status: "Loading video file...",
        progress: 20,
      }))
      const inputFileName = `input.${selectedFile.name.split(".").pop() || "mp4"}`

      await ffmpeg.writeFile(inputFileName, await fetchFile(selectedFile))

      if (clipSettings.mode === "single") {
        // Original single clip logic
        await processSingleClip(ffmpeg, inputFileName)
      } else {
        // New split video logic
        await processSplitVideo(ffmpeg, inputFileName)
      }
    } catch (error) {
      console.error("Processing error:", error)
      setProcessing({
        isProcessing: false,
        progress: 0,
        status: "",
        error:
          error instanceof Error
            ? error.message
            : "Video processing failed. Please try again.",
      })
    }
  }, [
    selectedFile,
    clipSettings,
    initializeFFmpeg,
    processSingleClip,
    processSplitVideo,
  ])

  return (
    <div className="container max-w-screen-lg mx-auto px-6 py-10 md:pt-20">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-3">
          <Scissors className="w-8 h-8 text-blue-600" />
          <h1
            className="text-3xl md:text-5xl font-bold"
            style={{
              viewTransitionName: "blog-title",
            }}
          >
            Video Clipper
          </h1>
        </div>
        {/* Add breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-1"
        >
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <span>Video Clipper</span>
        </nav>
        <p className="text-lg text-muted-foreground sm:text-xl font-light opacity-80 mb-6 max-w-2xl mx-auto">
          Convert long videos to multiple short clips instantly. Perfect for
          creating social media content. Powered by FFmpeg.wasm - everything
          happens securely in your browser.
        </p>
        <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Video className="w-4 h-4" />
            <span>Browser-based processing</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle2 className="w-4 h-4" />
            <span>No file uploads</span>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto space-y-8">
        {/* File Upload Zone */}
        <button
          type="button"
          ref={dropZoneRef}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
          aria-label="Upload video file"
          className={`relative w-full border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 bg-transparent ${
            isDragOver
              ? "border-blue-400 bg-blue-50/50 dark:border-blue-500 dark:bg-blue-950/20 scale-105"
              : selectedFile
                ? "border-green-300 bg-green-50/50 dark:border-green-700 dark:bg-green-950/20"
                : "border-gray-300 hover:border-gray-400 dark:border-gray-700 dark:hover:border-gray-600 hover:bg-gray-50/50 dark:hover:bg-gray-900/20"
          }`}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={(e) =>
              e.target.files?.[0] && handleFileSelect(e.target.files[0])
            }
            className="hidden"
          />

          {selectedFile ? (
            <div className="space-y-4">
              <CheckCircle2 className="w-16 h-16 mx-auto text-green-600" />
              <div>
                <h3 className="text-lg font-medium">{selectedFile.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB •{" "}
                  {selectedFile.type}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedFile(null)

                  // Clean up URLs
                  setDownloadUrl((prevUrl) => {
                    if (prevUrl) {
                      URL.revokeObjectURL(prevUrl)
                      objectUrlsRef.current = objectUrlsRef.current.filter(
                        (url) => url !== prevUrl
                      )
                    }
                    return null
                  })

                  setProcessedSegments((prevSegments) => {
                    prevSegments.forEach((segment) => {
                      if (segment.downloadUrl) {
                        URL.revokeObjectURL(segment.downloadUrl)
                        objectUrlsRef.current = objectUrlsRef.current.filter(
                          (url) => url !== segment.downloadUrl
                        )
                      }
                    })
                    return []
                  })

                  setProcessing((prev) => ({ ...prev, error: null }))
                }}
              >
                Choose Different File
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <Upload
                className={`w-16 h-16 mx-auto transition-colors ${isDragOver ? "text-blue-500" : "text-gray-400"}`}
              />
              <div>
                <h3 className="text-lg font-medium mb-2">
                  {isDragOver
                    ? "Drop your video here"
                    : "Select or drop your video"}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Supports MP4, WebM, AVI, MOV files up to 1GB
                </p>
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="w-4 h-4 mr-2" />
                  Select Video File
                </Button>
              </div>
            </div>
          )}
        </button>

        {/* Clip Settings */}
        {selectedFile && (
          <div className="p-6 border border-gray-200 rounded-xl bg-card dark:border-gray-800">
            <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
              <Scissors className="w-5 h-5" />
              Video Processing Settings
            </h3>

            {/* Mode Selection */}
            <div className="mb-6">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide mb-3">
                Processing Mode
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() =>
                    setClipSettings((prev) => ({ ...prev, mode: "single" }))
                  }
                  className={`p-4 border rounded-lg text-left transition-all ${
                    clipSettings.mode === "single"
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20 text-blue-900 dark:text-blue-100"
                      : "border-gray-300 hover:border-gray-400 dark:border-gray-700"
                  }`}
                >
                  <div className="font-medium mb-1">Single Clip</div>
                  <div className="text-sm text-muted-foreground">
                    Extract one specific time range
                  </div>
                </button>
                <button
                  type="button"
                  onClick={() =>
                    setClipSettings((prev) => ({ ...prev, mode: "split" }))
                  }
                  className={`p-4 border rounded-lg text-left transition-all ${
                    clipSettings.mode === "split"
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20 text-blue-900 dark:text-blue-100"
                      : "border-gray-300 hover:border-gray-400 dark:border-gray-700"
                  }`}
                >
                  <div className="font-medium mb-1">Split to Short Videos</div>
                  <div className="text-sm text-muted-foreground">
                    Create multiple short clips for social media
                  </div>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {clipSettings.mode === "single" ? (
                /* Single Clip Settings */
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Time Range
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label
                        htmlFor="start-time"
                        className="block text-sm font-medium mb-2"
                      >
                        Start (seconds)
                      </label>
                      <input
                        id="start-time"
                        type="number"
                        min="0"
                        max={videoDuration}
                        value={clipSettings.startTime}
                        onChange={(e) =>
                          setClipSettings((prev) => ({
                            ...prev,
                            startTime: Math.max(
                              0,
                              Math.min(
                                videoDuration - 1,
                                parseInt(e.target.value) || 0
                              )
                            ),
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-background focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="end-time"
                        className="block text-sm font-medium mb-2"
                      >
                        End (seconds)
                      </label>
                      <input
                        id="end-time"
                        type="number"
                        min="1"
                        max={videoDuration}
                        value={clipSettings.endTime}
                        onChange={(e) =>
                          setClipSettings((prev) => ({
                            ...prev,
                            endTime: Math.max(
                              prev.startTime + 1,
                              Math.min(
                                videoDuration,
                                parseInt(e.target.value) || 30
                              )
                            ),
                          }))
                        }
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-background focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Duration:{" "}
                    <span className="font-medium">
                      {Math.max(
                        0,
                        clipSettings.endTime - clipSettings.startTime
                      )}
                      s
                    </span>
                    {videoDuration > 0 && ` (Video: ${videoDuration}s total)`}
                  </p>
                </div>
              ) : (
                /* Split Video Settings */
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                    Split Settings
                  </h4>
                  <div>
                    <label
                      htmlFor="num-segments"
                      className="block text-sm font-medium mb-2"
                    >
                      Number of Segments
                    </label>
                    <input
                      id="num-segments"
                      type="number"
                      min="2"
                      max="20"
                      value={clipSettings.numSegments}
                      onChange={(e) => {
                        const numSegments = Math.max(
                          2,
                          Math.min(20, parseInt(e.target.value) || 3)
                        )
                        setClipSettings((prev) => ({
                          ...prev,
                          numSegments,
                          segmentDuration:
                            videoDuration > 0
                              ? Math.floor(videoDuration / numSegments)
                              : 30,
                        }))
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-background focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  {videoDuration > 0 && (
                    <div className="p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                      <p className="text-sm text-muted-foreground mb-1">
                        <span className="font-medium">Video Duration:</span>{" "}
                        {videoDuration}s
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <span className="font-medium">Each Segment:</span> ~
                        {Math.floor(videoDuration / clipSettings.numSegments)}s
                      </p>
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                        💡 Optimal for short-form content (15-60s per clip)
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Output Settings */}
              <div className="space-y-4">
                <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                  Output Settings
                </h4>
                <div className="space-y-3">
                  <div>
                    <label
                      htmlFor="output-format"
                      className="block text-sm font-medium mb-2"
                    >
                      Format
                    </label>
                    <select
                      id="output-format"
                      value={clipSettings.outputFormat}
                      onChange={(e) =>
                        setClipSettings((prev) => ({
                          ...prev,
                          outputFormat: e.target.value as "mp4" | "webm",
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-background focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="mp4">MP4 (H.264)</option>
                      <option value="webm">WebM (VP9)</option>
                    </select>
                  </div>
                  <div>
                    <label
                      htmlFor="output-quality"
                      className="block text-sm font-medium mb-2"
                    >
                      Quality
                    </label>
                    <select
                      id="output-quality"
                      value={clipSettings.quality}
                      onChange={(e) =>
                        setClipSettings((prev) => ({
                          ...prev,
                          quality: e.target.value as "high" | "medium" | "low",
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-background focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="high">High (Best quality)</option>
                      <option value="medium">Medium (Balanced)</option>
                      <option value="low">Low (Smaller file)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Controls */}
        {selectedFile && (
          <div className="text-center">
            <Button
              onClick={processVideo}
              disabled={
                processing.isProcessing ||
                (clipSettings.mode === "single" &&
                  clipSettings.endTime <= clipSettings.startTime)
              }
              size="lg"
              className="w-full md:w-auto px-8 py-3"
            >
              {processing.isProcessing ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Scissors className="w-4 h-4 mr-2" />
                  {clipSettings.mode === "split"
                    ? `Split into ${clipSettings.numSegments} Short Videos`
                    : "Create Video Clip"}
                </>
              )}
            </Button>
            {clipSettings.mode === "single" &&
              clipSettings.endTime <= clipSettings.startTime && (
                <p className="text-sm text-red-500 mt-2">
                  End time must be greater than start time
                </p>
              )}
          </div>
        )}

        {/* Progress */}
        {processing.isProcessing && (
          <div className="p-6 border border-blue-200 rounded-xl bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Processing Video
                </span>
                <span className="text-sm text-blue-700 dark:text-blue-300">
                  {processing.progress}%
                </span>
              </div>
              <Progress value={processing.progress} className="w-full h-2" />
              <p className="text-sm text-blue-800 dark:text-blue-200">
                {processing.status}
              </p>
            </div>
          </div>
        )}

        {/* Error Display */}
        {processing.error && (
          <Alert
            variant="destructive"
            className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20"
          >
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-800 dark:text-red-200">
              {processing.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Download Section - Single Clip */}
        {downloadUrl && clipSettings.mode === "single" && (
          <div className="p-8 border border-green-200 rounded-xl bg-green-50/50 dark:border-green-800 dark:bg-green-950/20 text-center">
            <CheckCircle2 className="w-16 h-16 mx-auto text-green-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-green-900 dark:text-green-100">
              Video Clip Ready!
            </h3>
            <p className="text-sm text-green-800 dark:text-green-200 mb-6">
              Your {clipSettings.endTime - clipSettings.startTime}s video clip
              has been processed successfully.
            </p>
            <Button
              asChild
              size="lg"
              className="bg-green-600 hover:bg-green-700"
            >
              <a
                href={downloadUrl}
                download={`clip-${clipSettings.startTime}s-${clipSettings.endTime}s.${clipSettings.outputFormat}`}
              >
                <Download className="w-4 h-4 mr-2" />
                Download {clipSettings.outputFormat.toUpperCase()} File
              </a>
            </Button>
          </div>
        )}

        {/* Download Section - Multiple Segments */}
        {processedSegments.length > 0 && clipSettings.mode === "split" && (
          <div className="p-8 border border-green-200 rounded-xl bg-green-50/50 dark:border-green-800 dark:bg-green-950/20">
            <div className="text-center mb-6">
              <CheckCircle2 className="w-16 h-16 mx-auto text-green-600 mb-4" />
              <h3 className="text-xl font-semibold mb-2 text-green-900 dark:text-green-100">
                {processedSegments.length} Short Videos Ready!
              </h3>
              <p className="text-sm text-green-800 dark:text-green-200">
                Your video has been split into {processedSegments.length} short
                clips, perfect for social media.
              </p>
            </div>

            {/* Download All Button */}
            <div className="text-center mb-6">
              <Button
                onClick={() => {
                  processedSegments.forEach((segment) => {
                    if (segment.downloadUrl) {
                      const link = document.createElement("a")
                      link.href = segment.downloadUrl
                      link.download = `short-video-${segment.index}.${clipSettings.outputFormat}`
                      document.body.appendChild(link)
                      link.click()
                      document.body.removeChild(link)
                    }
                  })
                }}
                size="lg"
                className="bg-green-600 hover:bg-green-700"
              >
                <Download className="w-4 h-4 mr-2" />
                Download All ({processedSegments.length} files)
              </Button>
            </div>

            {/* Individual Segment Downloads */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {processedSegments.map((segment) => (
                <div
                  key={segment.index}
                  className="p-4 border border-green-300 rounded-lg bg-white/50 dark:bg-green-950/10"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-green-900 dark:text-green-100">
                      Segment {segment.index}
                    </h4>
                    <span className="text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded">
                      {Math.round(segment.endTime - segment.startTime)}s
                    </span>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300 mb-3">
                    {Math.floor(segment.startTime / 60)}:
                    {(segment.startTime % 60).toString().padStart(2, "0")} -{" "}
                    {Math.floor(segment.endTime / 60)}:
                    {(segment.endTime % 60).toString().padStart(2, "0")}
                  </p>
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="w-full border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/30"
                  >
                    <a
                      href={segment.downloadUrl}
                      download={`short-video-${segment.index}.${clipSettings.outputFormat}`}
                    >
                      <Download className="w-3 h-3 mr-1" />
                      Download
                    </a>
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-8 border-t border-gray-200 dark:border-gray-800">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
              <Video className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold">Privacy First</h3>
            <p className="text-sm text-muted-foreground">
              All processing happens locally in your browser. Your videos never
              leave your device.
            </p>
          </div>
          <div className="text-center space-y-3">
            <div className="w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
              <Scissors className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="font-semibold">Professional Quality</h3>
            <p className="text-sm text-muted-foreground">
              Powered by FFmpeg.wasm with multiple quality and format options
              for optimal results.
            </p>
          </div>
          <div className="text-center space-y-3">
            <div className="w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
              <Download className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-semibold">Instant Results</h3>
            <p className="text-sm text-muted-foreground">
              Fast processing with automatic download and browser notifications
              when complete.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
