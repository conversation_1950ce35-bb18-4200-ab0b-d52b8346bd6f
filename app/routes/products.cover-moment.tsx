import { Download, Image as ImageIcon, Palette, Type } from "lucide-react"
import type { ChangeEvent } from "react"
import { useCallback, useEffect, useId, useRef, useState } from "react"
import { Button } from "~/components/ui/button"
import { Label } from "~/components/ui/label"
import { Textarea } from "~/components/ui/textarea"
import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title:
      "Cover Moment - Free YouTube Thumbnail & Video Cover Design Tool | Henry Zhang",
    description:
      "Free online video thumbnail design tool for YouTube, Instagram, and social media. Create professional video covers with custom fonts, drag-and-drop images, and color customization. Browser-based processing ensures data privacy.",
    keywords:
      "video thumbnail maker, YouTube thumbnail creator, video cover design, thumbnail generator, social media graphics, free design tool, custom fonts, drag and drop editor, thumbnail templates, video marketing, Cover Moment, YouTube thumbnails, Instagram covers",
    ogImage: "https://zhanghe.dev/cover-moment-og.jpg",
    ogUrl: "https://zhanghe.dev/products/cover-moment",
  })
}

// Font configuration
const FONTS = [
  // Popular English fonts
  {
    name: "Inter",
    displayName: "Inter",
    value: "'Inter', sans-serif",
    googleFont: "Inter:wght@300;400;500;700;900",
    category: "english",
  },
  {
    name: "Roboto",
    displayName: "Roboto",
    value: "'Roboto', sans-serif",
    googleFont: "Roboto:wght@300;400;500;700;900",
    category: "english",
  },
  {
    name: "Montserrat",
    displayName: "Montserrat",
    value: "'Montserrat', sans-serif",
    googleFont: "Montserrat:wght@300;400;500;700;900",
    category: "english",
  },
  {
    name: "Poppins",
    displayName: "Poppins",
    value: "'Poppins', sans-serif",
    googleFont: "Poppins:wght@300;400;500;700;900",
    category: "english",
  },
  {
    name: "Playfair Display",
    displayName: "Playfair Display",
    value: "'Playfair Display', serif",
    googleFont: "Playfair+Display:wght@300;400;500;700;900",
    category: "english",
  },
  {
    name: "Open Sans",
    displayName: "Open Sans",
    value: "'Open Sans', sans-serif",
    googleFont: "Open+Sans:wght@300;400;500;700;900",
    category: "english",
  },
  // Chinese fonts
  {
    name: "Noto Sans SC",
    displayName: "思源黑体",
    value: "'Noto Sans SC', sans-serif",
    googleFont: "Noto+Sans+SC:wght@300;400;500;700;900",
    category: "chinese",
  },
  {
    name: "Source Han Sans SC",
    displayName: "源诺黑体",
    value: "'Source Han Sans SC', sans-serif",
    googleFont: "Source+Han+Sans+SC:wght@300;400;500;700;900",
    category: "chinese",
  },
  {
    name: "Noto Serif SC",
    displayName: "思源宋体",
    value: "'Noto Serif SC', serif",
    googleFont: "Noto+Serif+SC:wght@300;400;500;700;900",
    category: "chinese",
  },
  {
    name: "Ma Shan Zheng",
    displayName: "马善政楷体",
    value: "'Ma Shan Zheng', cursive",
    googleFont: "Ma+Shan+Zheng:wght@400",
    category: "chinese",
  },
  {
    name: "ZCOOL XiaoWei",
    displayName: "站酷小薇体",
    value: "'ZCOOL XiaoWei', serif",
    googleFont: "ZCOOL+XiaoWei:wght@400",
    category: "chinese",
  },
  {
    name: "ZCOOL QingKe HuangYou",
    displayName: "站酷庆科黄油体",
    value: "'ZCOOL QingKe HuangYou', sans-serif",
    googleFont: "ZCOOL+QingKe+HuangYou:wght@400",
    category: "chinese",
  },
]

// Popular color configuration
const POPULAR_COLORS = [
  { name: "Pure White", value: "#FFFFFF" },
  { name: "Classic Black", value: "#000000" },
  { name: "Vibrant Red", value: "#EF4444" },
  { name: "Bright Yellow", value: "#F59E0B" },
  { name: "Fresh Green", value: "#10B981" },
  { name: "Sky Blue", value: "#3B82F6" },
  { name: "Deep Blue", value: "#1E40AF" },
  { name: "Mysterious Purple", value: "#8B5CF6" },
  { name: "Rose Pink", value: "#EC4899" },
  { name: "Modern Gray", value: "#6B7280" },
  { name: "Gold", value: "#D97706" },
  { name: "Dark Red", value: "#B91C1C" },
]

interface CoverSettings {
  title: string
  font: string
  color: string
  backgroundImage: string | null
  imagePosition: { x: number; y: number }
  imageSize: { width: number; height: number }
}

export default function CoverMoment() {
  const exportHelpId = useId()
  const titleId = useId()
  const titleHelpId = useId()
  const [settings, setSettings] = useState<CoverSettings>({
    title: "Your Video Title",
    font: FONTS[0].value,
    color: POPULAR_COLORS[1].value, // Default to black
    backgroundImage: null,
    imagePosition: { x: 0, y: 0 },
    imageSize: { width: 0, height: 0 },
  })

  const [selectedFont, setSelectedFont] = useState(FONTS[0])
  const [isGenerating, setIsGenerating] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [hasDragged, setHasDragged] = useState(false)
  const canvasRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 获取容器尺寸的公用函数
  const getContainerSize = useCallback(() => {
    if (!canvasRef.current) return { width: 320, height: 192 }

    const rect = canvasRef.current.getBoundingClientRect()
    return { width: rect.width, height: rect.height / 2 }
  }, [])

  // 计算图片 cover 效果的尺寸和位置
  const calculateCoverSize = useCallback(
    (imgWidth: number, imgHeight: number) => {
      const { width: containerWidth, height: containerHeight } =
        getContainerSize()

      const scaleByWidth = containerWidth / imgWidth
      const scaleByHeight = containerHeight / imgHeight
      const scale = Math.max(scaleByWidth, scaleByHeight)

      const displayWidth = imgWidth * scale
      const displayHeight = imgHeight * scale
      const initialX = -(displayWidth - containerWidth) / 2
      const initialY = -(displayHeight - containerHeight) / 2

      return {
        size: { width: displayWidth, height: displayHeight },
        position: { x: initialX, y: initialY },
      }
    },
    [getContainerSize]
  )

  // 处理拖拽边界限制
  const constrainPosition = useCallback(
    (newX: number, newY: number) => {
      const { width: containerWidth, height: containerHeight } =
        getContainerSize()
      const maxX = 0
      const minX = containerWidth - settings.imageSize.width
      const maxY = 0
      const minY = containerHeight - settings.imageSize.height

      return {
        x: Math.max(minX, Math.min(maxX, newX)),
        y: Math.max(minY, Math.min(maxY, newY)),
      }
    },
    [getContainerSize, settings.imageSize]
  )

  // Dynamically load Google Fonts
  useEffect(() => {
    const loadGoogleFont = (fontUrl: string) => {
      const linkId = `google-font-${fontUrl.replace(/[^a-zA-Z0-9]/g, "-")}`
      if (!document.getElementById(linkId)) {
        const link = document.createElement("link")
        link.id = linkId
        link.href = `https://fonts.googleapis.com/css2?family=${fontUrl}&display=swap`
        link.rel = "stylesheet"
        document.head.appendChild(link)
      }
    }

    // Load all fonts on initial mount
    FONTS.forEach((font) => {
      loadGoogleFont(font.googleFont)
    })
  }, [])

  // Load font when selected to ensure it's available
  useEffect(() => {
    if (selectedFont) {
      const loadGoogleFont = (fontUrl: string) => {
        const linkId = `google-font-${fontUrl.replace(/[^a-zA-Z0-9]/g, "-")}`
        if (!document.getElementById(linkId)) {
          const link = document.createElement("link")
          link.id = linkId
          link.href = `https://fonts.googleapis.com/css2?family=${fontUrl}&display=swap`
          link.rel = "stylesheet"
          document.head.appendChild(link)
        }
      }
      loadGoogleFont(selectedFont.googleFont)
    }
  }, [selectedFont])

  // 根据标题长度计算字体大小
  const calculateFontSize = useCallback((title: string) => {
    const length = title.length
    if (length <= 10) return "text-4xl" // 36px
    if (length <= 20) return "text-3xl" // 30px
    if (length <= 30) return "text-2xl" // 24px
    if (length <= 40) return "text-xl" // 20px
    return "text-lg" // 18px
  }, [])

  const handleTitleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setSettings((prev) => ({ ...prev, title: e.target.value }))
  }

  const handleFontChange = (fontIndex: number) => {
    const font = FONTS[fontIndex]
    setSelectedFont(font)
    setSettings((prev) => ({ ...prev, font: font.value }))
  }

  const handleColorChange = (color: string) => {
    setSettings((prev) => ({ ...prev, color }))
  }

  const processImageFile = (file: File) => {
    if (!file.type.startsWith("image/")) return

    const reader = new FileReader()
    reader.onload = (event) => {
      const img = new Image()
      img.onload = () => {
        const { size, position } = calculateCoverSize(img.width, img.height)
        setSettings((prev) => ({
          ...prev,
          backgroundImage: event.target?.result as string,
          imagePosition: position,
          imageSize: size,
        }))
      }
      img.src = event.target?.result as string
    }
    reader.readAsDataURL(file)
  }

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) processImageFile(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const file = e.dataTransfer.files[0]
    if (file) processImageFile(file)
  }

  const handleImageAreaClick = (_e: React.MouseEvent) => {
    // 如果刚刚结束拖拽，则不触发上传
    if (hasDragged) {
      setHasDragged(false)
      return
    }

    // 只有点击空白区域或直接点击（非拖拽）才触发上传
    fileInputRef.current?.click()
  }

  // 拖拽处理
  const updateImagePosition = useCallback(
    (clientX: number, clientY: number) => {
      if (!isDragging || !settings.backgroundImage) return

      const newX = clientX - dragStart.x
      const newY = clientY - dragStart.y
      const constrainedPosition = constrainPosition(newX, newY)

      // 标记为已拖拽
      setHasDragged(true)

      setSettings((prev) => ({
        ...prev,
        imagePosition: constrainedPosition,
      }))
    },
    [isDragging, dragStart, settings.backgroundImage, constrainPosition]
  )

  const handleImageMouseDown = (e: React.MouseEvent) => {
    if (!settings.backgroundImage) return
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setHasDragged(false) // 重置拖拽标记
    setDragStart({
      x: e.clientX - settings.imagePosition.x,
      y: e.clientY - settings.imagePosition.y,
    })
  }

  const handleImageMouseMove = (e: React.MouseEvent) => {
    updateImagePosition(e.clientX, e.clientY)
  }

  // 全局鼠标事件监听
  useEffect(() => {
    if (!isDragging) return

    const handleGlobalMouseMove = (e: MouseEvent) => {
      e.preventDefault()
      updateImagePosition(e.clientX, e.clientY)
    }

    const handleGlobalMouseUp = () => setIsDragging(false)

    document.addEventListener("mousemove", handleGlobalMouseMove)
    document.addEventListener("mouseup", handleGlobalMouseUp)

    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove)
      document.removeEventListener("mouseup", handleGlobalMouseUp)
    }
  }, [isDragging, updateImagePosition])

  const exportToImage = async () => {
    if (!canvasRef.current) return

    setIsGenerating(true)

    try {
      // 动态导入 snapdom
      const { snapdom } = await import("@zumer/snapdom")

      // 使用 snapdom 捕获元素并转换为 PNG
      const result = await snapdom(canvasRef.current, {
        scale: 2, // 高分辨率输出
        backgroundColor: "#ffffff", // 白色背景
      })

      // 直接下载图片
      await result.download({
        format: "png",
        filename: `cover-moment-${Date.now()}`,
      })
    } catch (error) {
      console.error("Export failed:", error)
      alert("Export failed, please try again")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <>
      {/* 主要内容区域 - 添加语义化标签 */}
      <header className="container max-w-screen-md mx-auto min-h-[256px] p-8 gap-6 flex flex-col justify-end">
        <h1 className="text-5xl font-bold">Cover Moment</h1>
        <p className="opacity-80">
          Professional video thumbnail design tool with custom text, image
          upload, and multiple font and color options
        </p>
        {/* Add breadcrumb navigation for SEO */}
        <nav aria-label="Breadcrumb navigation" className="text-sm opacity-60">
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <span>Cover Moment</span>
        </nav>
      </header>

      <main className="container max-w-screen-lg mx-auto p-8">
        {/* 添加文章/内容结构 */}
        <article>
          {/* 工具介绍部分 */}
          <section className="mb-8">
            <h2 className="sr-only">Tool Introduction</h2>
            <p className="text-lg text-muted-foreground mb-4">
              Cover Moment is a free video thumbnail design tool created for
              content creators. Supports YouTube, Instagram, TikTok and other
              major platforms with rich font selections and color customization
              options.
            </p>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">🎨 Rich Fonts</h3>
                <p className="text-sm text-muted-foreground">
                  6 English + 6 Chinese fonts
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">📱 Multi-Platform</h3>
                <p className="text-sm text-muted-foreground">
                  YouTube/Instagram/TikTok
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">🔒 Data Security</h3>
                <p className="text-sm text-muted-foreground">
                  Local processing, no uploads
                </p>
              </div>
              <div className="bg-card rounded-lg p-4 text-center">
                <h3 className="font-semibold mb-2">💾 HD Export</h3>
                <p className="text-sm text-muted-foreground">
                  PNG format HD output
                </p>
              </div>
            </div>
          </section>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* 左侧：预览区域 */}
            <section className="space-y-6">
              <header className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">Live Preview</h2>
                <Button
                  onClick={exportToImage}
                  disabled={isGenerating || !settings.backgroundImage}
                  className="gap-2"
                  aria-describedby={exportHelpId}
                >
                  <Download className="w-4 h-4" />
                  {isGenerating ? "Generating..." : "Export Image"}
                </Button>
              </header>
              <div id={exportHelpId} className="sr-only">
                Click the export button to generate a high-definition PNG format
                thumbnail image and automatically download it locally
              </div>

              {/* 画布预览 */}
              <div className="bg-card rounded-lg p-6">
                <div className="flex justify-center">
                  <div
                    ref={canvasRef}
                    className="relative aspect-[3/4] w-full max-w-md bg-white rounded-lg overflow-hidden"
                    role="img"
                    aria-label="Video thumbnail preview"
                  >
                    {/* 标题区域 */}
                    <div className="absolute top-0 left-0 right-0 h-1/2 flex items-center justify-center p-6 z-10">
                      <h3
                        className={`${calculateFontSize(
                          settings.title
                        )} font-bold text-center leading-tight break-words max-w-full`}
                        style={{
                          fontFamily: settings.font,
                          color: settings.color,
                        }}
                      >
                        {settings.title}
                      </h3>
                    </div>

                    {/* 图片区域 */}
                    <button
                      type="button"
                      className={`absolute bottom-0 left-0 right-0 h-1/2 overflow-hidden border-none bg-transparent p-0 ${
                        settings.backgroundImage
                          ? "cursor-move"
                          : "cursor-pointer"
                      }`}
                      onClick={handleImageAreaClick}
                      onDragOver={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                      }}
                      onDrop={handleDrop}
                      aria-label={
                        settings.backgroundImage
                          ? "Drag to adjust background image position"
                          : "Click to upload background image"
                      }
                    >
                      {settings.backgroundImage ? (
                        <img
                          src={settings.backgroundImage}
                          alt="User uploaded background"
                          className={`absolute select-none ${
                            isDragging ? "cursor-grabbing" : "cursor-grab"
                          }`}
                          style={{
                            width: `${settings.imageSize.width}px`,
                            height: `${settings.imageSize.height}px`,
                            minWidth: `${settings.imageSize.width}px`,
                            minHeight: `${settings.imageSize.height}px`,
                            maxWidth: `${settings.imageSize.width}px`,
                            maxHeight: `${settings.imageSize.height}px`,
                            transform: `translate(${settings.imagePosition.x}px, ${settings.imagePosition.y}px)`,
                            transition: isDragging
                              ? "none"
                              : "transform 0.1s ease-out",
                          }}
                          onMouseDown={handleImageMouseDown}
                          onMouseMove={handleImageMouseMove}
                          draggable={false}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center border-t hover:bg-muted/80 transition-colors">
                          <div className="text-muted-foreground text-center">
                            <ImageIcon className="w-12 h-12 mx-auto mb-2" />
                            <p className="text-sm">
                              Click or drag to upload background image
                            </p>
                          </div>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </section>

            {/* 右侧：控制面板 */}
            <aside className="space-y-6">
              {/* 文字设置 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <Type className="w-5 h-5" />
                  Text Settings
                </h3>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor={titleId} className="text-sm font-medium">
                      Title Text
                    </Label>
                    <Textarea
                      id={titleId}
                      value={settings.title}
                      onChange={handleTitleChange}
                      placeholder="Enter your video title..."
                      rows={3}
                      className="mt-1 resize-none"
                      aria-describedby={titleHelpId}
                    />
                    <p
                      id={titleHelpId}
                      className="text-xs text-muted-foreground mt-1"
                    >
                      Current character count: {settings.title.length} • More
                      text = smaller font
                    </p>
                  </div>

                  <fieldset>
                    <legend className="text-sm font-medium mb-2">
                      Font Selection
                    </legend>
                    <div className="grid grid-cols-3 gap-2">
                      {FONTS.map((font, index) => (
                        <Button
                          key={font.value}
                          variant={
                            selectedFont.name === font.name
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => handleFontChange(index)}
                          className="justify-start"
                          style={{ fontFamily: font.value }}
                          aria-pressed={selectedFont.name === font.name}
                        >
                          <div className="flex items-center gap-1">
                            {font.category === "chinese" && (
                              <>
                                <span className="sr-only">Chinese font</span>
                                <span className="text-xs" aria-hidden="true">
                                  🇨🇳
                                </span>
                              </>
                            )}
                            {font.displayName}
                          </div>
                        </Button>
                      ))}
                    </div>
                  </fieldset>
                </div>
              </section>

              {/* 颜色设置 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Color Settings
                </h3>

                <fieldset>
                  <legend className="text-sm font-medium mb-2">
                    Text Color
                  </legend>
                  <div className="grid grid-cols-4 gap-3">
                    {POPULAR_COLORS.map((color) => (
                      <Button
                        key={color.value}
                        variant={
                          settings.color === color.value ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => handleColorChange(color.value)}
                        className="p-3 h-auto flex flex-col items-center gap-2"
                        aria-pressed={settings.color === color.value}
                        aria-label={`Select ${color.name} color`}
                      >
                        <div
                          className="w-6 h-6 rounded-full border"
                          style={{ backgroundColor: color.value }}
                        />
                        <span className="text-xs">{color.name}</span>
                      </Button>
                    ))}
                  </div>
                </fieldset>
              </section>

              {/* 导出说明 */}
              <section className="bg-muted rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">Export Information</h3>
                <div className="space-y-2 text-sm opacity-80">
                  <p>• Export size: 1080 × 1440 pixels (3:4 ratio)</p>
                  <p>• Format: PNG high-definition image</p>
                  <p>
                    • Compatible platforms: YouTube, Instagram, TikTok, etc.
                  </p>
                  <p>• Processing method: Local generation, data security</p>
                </div>
              </section>

              {/* SEO 友好的功能说明 */}
              <section className="bg-card rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">
                  Why Choose Cover Moment?
                </h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    ✅ <strong>Completely Free</strong>: No registration
                    required, no watermarks, permanently free to use
                  </li>
                  <li>
                    ✅ <strong>Data Security</strong>: All processing is
                    completed locally in the browser, no data uploads
                  </li>
                  <li>
                    ✅ <strong>Professional Quality</strong>: Supports HD
                    export, compatible with major social media platforms
                  </li>
                  <li>
                    ✅ <strong>Multi-Language Support</strong>: 6 English + 6
                    Chinese fonts, perfect for bilingual typography
                  </li>
                  <li>
                    ✅ <strong>Easy to Use</strong>: No design experience
                    needed, 3 steps to create professional thumbnails
                  </li>
                </ul>
              </section>
            </aside>
          </div>
        </article>
      </main>

      {/* 隐藏的文件输入框 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        style={{ display: "none" }}
        aria-label="Upload background image"
      />
    </>
  )
}
