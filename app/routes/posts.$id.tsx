import { eq } from "drizzle-orm"
import ReactMarkdown from "react-markdown"
import { data } from "react-router"
import Author from "~/components/author"
import * as schema from "~/database/schema"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/posts.$id"

export function meta({ data: routeData }: Route.MetaArgs) {
  const { post } = routeData || {}

  if (!post) {
    return createMeta({
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    })
  }

  return createMeta({
    title: post.title,
    description: post.excerpt || post.title,
    keywords: `blog, ${post.title}, <PERSON>, web development`,
    ogUrl: `https://zhanghe.dev/posts/${post.id}`,
  })
}

export async function loader({ context, params }: Route.LoaderArgs) {
  try {
    const postId = Number(params.id)
    if (Number.isNaN(postId)) {
      throw data("Invalid post ID", { status: 400 })
    }

    const post = await context.db.query.posts.findFirst({
      where: eq(schema.posts.id, postId),
    })

    if (!post) {
      throw data("Post not found", { status: 404 })
    }

    return { post }
  } catch (error) {
    console.error("Error loading post:", error)
    if (error instanceof Response) throw error
    throw data("Failed to load post", { status: 500 })
  }
}

export default function PostDetail({ loaderData }: Route.ComponentProps) {
  const { post } = loaderData

  return (
    <div className="container max-w-screen-md mx-auto px-6 py-10 md:pt-20">
      <header className="mb-8">
        <h1
          className="text-3xl md:text-5xl font-bold inline-block mb-6"
          style={{
            viewTransitionName: "blog-title",
          }}
        >
          {post?.title}
        </h1>
        <Author />
      </header>

      <article className="prose lg:prose-xl prose-img:rounded-xl dark:prose-invert">
        <ReactMarkdown>{post?.content || ""}</ReactMarkdown>
      </article>
    </div>
  )
}
