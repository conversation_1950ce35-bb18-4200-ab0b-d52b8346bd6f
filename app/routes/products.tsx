import { Outlet } from "react-router"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "Products - Henry Zhang",
    description:
      "Discover useful tools and products designed to enhance your digital productivity and creativity.",
    keywords:
      "products, tools, apps, extensions, web development, productivity",
    ogUrl: "https://zhanghe.dev/products",
  })
}

export async function loader(_: Route.LoaderArgs) {
  // Layout loader - could load featured products here
  return { productsInfo: { featuredCount: 6 } }
}

export default function ProductsLayout(_: Route.ComponentProps) {
  return <Outlet />
}
