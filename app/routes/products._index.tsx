import { ArrowRight, Download, Globe, Shield, Zap } from "lucide-react"
import { useId } from "react"
import { ProductGrid } from "~/components/product-grid"
import { But<PERSON> } from "~/components/ui/button"
import { createMeta, PRODUCTS } from "~/lib/route-utils"
import type { Route } from "./+types/products._index"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title:
      "Free AI Tools & Web Applications by <PERSON> | Product Collection",
    description:
      "Discover free AI-powered tools and web applications designed for content creators, developers, and digital professionals. Video editors, Chrome extensions, and productivity tools - all completely free.",
    keywords:
      "free AI tools, web applications, Chrome extensions, video editing tools, productivity apps, content creation software, developer tools, digital marketing tools, browser extensions, online utilities, AI translation, video converter, Git commit analyzer, lyric downloader",
    ogUrl: "https://zhanghe.dev/products",
  })
}

export async function loader(_: Route.LoaderArgs) {
  return { products: PRODUCTS }
}

export default function ProductsIndex({ loaderData }: Route.ComponentProps) {
  const { products } = loaderData
  const productGridId = useId()

  // Enhanced product data with additional SEO metadata
  const enhancedProducts = products.map((product) => ({
    ...product,
    category: getProductCategory(product.id),
    icon: getProductIcon(product.id),
    badge: getProductBadge(product.id),
  }))

  return (
    <>
      {/* Enhanced Hero Section with SEO elements */}
      <section className="container max-w-screen-xl mx-auto px-6 py-16 md:pt-20">
        {/* Breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-6"
        >
          <a href="/" className="hover:opacity-80 transition-opacity">
            Home
          </a>
          <span className="mx-2">/</span>
          <span className="text-foreground">Products</span>
        </nav>

        <div className="text-center mb-12">
          <h1
            className="text-4xl md:text-6xl font-bold mb-6"
            style={{ viewTransitionName: "page-title" }}
          >
            Free AI Tools &amp; Applications
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground font-light mb-8 max-w-3xl mx-auto">
            Discover powerful, free tools designed for content creators,
            developers, and digital professionals. All applications are
            privacy-focused and built with modern web technologies.
          </p>

          {/* Key stats for SEO and credibility */}
          <div className="flex items-center justify-center gap-8 text-sm text-muted-foreground mb-8">
            <div className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              <span>100% Free</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              <span>Privacy-First</span>
            </div>
            <div className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              <span>Open Source</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              <span>Lightning Fast</span>
            </div>
          </div>

          <Button asChild size="lg" className="group">
            <a href="#product-grid">
              Explore All Tools
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
        </div>

        {/* Featured Categories for SEO */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-16">
          <div className="text-center p-6 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
            <Globe className="w-8 h-8 mx-auto mb-3 text-blue-600" />
            <h3 className="font-semibold mb-1">Translation Tools</h3>
            <p className="text-sm text-muted-foreground">
              AI-powered privacy-first translation
            </p>
          </div>
          <div className="text-center p-6 rounded-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
            <Download className="w-8 h-8 mx-auto mb-3 text-green-600" />
            <h3 className="font-semibold mb-1">Video Tools</h3>
            <p className="text-sm text-muted-foreground">
              Professional video processing
            </p>
          </div>
          <div className="text-center p-6 rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20">
            <Zap className="w-8 h-8 mx-auto mb-3 text-purple-600" />
            <h3 className="font-semibold mb-1">Developer Tools</h3>
            <p className="text-sm text-muted-foreground">
              Productivity utilities for developers
            </p>
          </div>
          <div className="text-center p-6 rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20">
            <Shield className="w-8 h-8 mx-auto mb-3 text-orange-600" />
            <h3 className="font-semibold mb-1">Browser Extensions</h3>
            <p className="text-sm text-muted-foreground">
              Chrome extensions for productivity
            </p>
          </div>
        </div>

        {/* Product Grid with enhanced SEO */}
        <div className="max-w-6xl mx-auto" id={productGridId}>
          <h2 className="text-3xl font-bold text-center mb-12">All Products</h2>

          {/* Group products by category for better SEO structure */}
          <div className="space-y-12">
            {Object.entries(
              enhancedProducts.reduce(
                (acc, product) => {
                  if (!acc[product.category]) acc[product.category] = []
                  acc[product.category].push(product)
                  return acc
                },
                {} as Record<string, typeof enhancedProducts>
              )
            ).map(([category, categoryProducts]) => (
              <section key={category} className="scroll-mt-20">
                <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                  {getCategoryIcon(category)}
                  {category}
                </h3>
                <ProductGrid products={categoryProducts} columns="3" />
              </section>
            ))}
          </div>
        </div>

        {/* SEO-optimized footer with internal links */}
        <div className="mt-20 pt-12 border-t">
          <div className="text-center">
            <h3 className="text-2xl font-semibold mb-4">
              Why Choose Our Tools?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Shield className="w-4 h-4 text-green-600" />
                  Privacy-First
                </h4>
                <p className="text-sm text-muted-foreground">
                  All tools are designed with privacy in mind. No unnecessary
                  data collection, no telemetry, and local processing when
                  possible.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Download className="w-4 h-4 text-blue-600" />
                  Open Source
                </h4>
                <p className="text-sm text-muted-foreground">
                  Most tools are open source with MIT licenses. Transparent,
                  auditable, and free to modify for your needs.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Zap className="w-4 h-4 text-purple-600" />
                  Modern Technology
                </h4>
                <p className="text-sm text-muted-foreground">
                  Built with cutting-edge web technologies including
                  WebAssembly, modern browsers APIs, and AI integration.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

// Helper functions for enhanced product data
function getProductCategory(productId: string): string {
  const categories: Record<string, string> = {
    "native-translate": "Translation Tools",
    "git-commit-analyzer": "Developer Tools",
    "cover-moment": "Video Tools",
    "video-clipper": "Video Tools",
    "suno-lyric-downloader": "Media Tools",
    alchemy: "Creative Tools",
    mangoflow: "Browser Extensions",
  }
  return categories[productId] || "Other Tools"
}

function getProductIcon(productId: string) {
  const icons: Record<string, React.ReactNode> = {
    "native-translate": <Globe className="w-5 h-5" />,
    "git-commit-analyzer": <Zap className="w-5 h-5" />,
    "cover-moment": <Download className="w-5 h-5" />,
    "video-clipper": <Download className="w-5 h-5" />,
    "suno-lyric-downloader": <Download className="w-5 h-5" />,
    alchemy: <Zap className="w-5 h-5" />,
    mangoflow: <Globe className="w-5 h-5" />,
  }
  return icons[productId] || <Zap className="w-5 h-5" />
}

function getCategoryIcon(category: string) {
  const iconMap: Record<string, React.ReactNode> = {
    "Translation Tools": <Globe className="w-5 h-5 text-blue-600" />,
    "Developer Tools": <Zap className="w-5 h-5 text-purple-600" />,
    "Video Tools": <Download className="w-5 h-5 text-green-600" />,
    "Media Tools": <Download className="w-5 h-5 text-orange-600" />,
    "Creative Tools": <Zap className="w-5 h-5 text-pink-600" />,
    "Browser Extensions": <Globe className="w-5 h-5 text-indigo-600" />,
    "Other Tools": <Zap className="w-5 h-5 text-gray-600" />,
  }
  return iconMap[category] || <Zap className="w-5 h-5 text-gray-600" />
}

function getProductBadge(productId: string): string | undefined {
  const badges: Record<string, string> = {
    "native-translate": "New",
    "video-clipper": "Popular",
  }
  return badges[productId]
}
