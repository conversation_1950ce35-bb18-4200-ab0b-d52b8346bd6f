import { Outlet } from "react-router"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/admin"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "Admin Dashboard | Henry Zhang - Content Management",
    description:
      "Secure administrative dashboard for managing website content, blog posts, and product information. Access restricted to authorized personnel only.",
    keywords:
      "admin dashboard, content management, website administration, blog management, product management, secure admin panel",
    ogUrl: "https://zhanghe.dev/admin",
  })
}

export async function loader(_: Route.LoaderArgs) {
  // Future: Add authentication check here
  return { authenticated: true }
}

export default function AdminLayout(_: Route.ComponentProps) {
  return <Outlet />
}
