import {
  Download,
  FileText,
  Globe,
  Lock,
  Music,
  Shield,
  Zap,
} from "lucide-react"
import { <PERSON> } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/products.suno-lyric-downloader._index"

export function meta() {
  return createMeta({
    title:
      "Suno Lyric Downloader - Chrome Extension for Synchronized Lyrics",
    description:
      "Chrome extension to download word-level synchronized lyrics from Suno.com in LRC and SRT formats. Automatic detection, precise timing, and completely offline operation.",
    keywords:
      "Suno lyric downloader, Chrome extension, synchronized lyrics, LRC format, SRT format, Suno.com lyrics, word-level timing, karaoke lyrics, subtitle lyrics, music lyrics download, browser extension, offline lyrics, Suno songs, synchronized text, music player lyrics, video subtitles",
    ogUrl: "https://zhanghe.dev/products/suno-lyric-downloader",
    ogImage: "https://assets.zhanghe.dev/suno-lyric-downloader/og.jpg",
  })
}

export default function SunoLyricDownloader(_: Route.ComponentProps) {
  return (
    <div className="container max-w-screen-xl mx-auto px-6 py-10 md:pt-20">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Music className="w-8 h-8 text-blue-600" />
          <h1
            className="text-3xl md:text-5xl font-bold"
            style={{
              viewTransitionName: "blog-title",
            }}
          >
            Suno Lyric Downloader
          </h1>
        </div>

        {/* Breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-4"
        >
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <span>Suno Lyric Downloader</span>
        </nav>

        <p className="text-xl md:text-2xl text-muted-foreground font-light mb-8 max-w-4xl mx-auto">
          Download word-level synchronized lyrics from Suno.com in LRC and SRT
          formats. Automatic detection, precise timing, and completely secure.
        </p>

        <div className="flex flex-wrap items-center justify-center gap-4 text-sm text-muted-foreground mb-8">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            <span>Secure authentication</span>
          </div>
          <div className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            <span>Open-source</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            <span>Fast & lightweight</span>
          </div>
          <div className="flex items-center gap-2">
            <Lock className="w-4 h-4" />
            <Link
              to="/products/suno-lyric-downloader/privacy"
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              Privacy Policy
            </Link>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button className="cursor-pointer" size="lg">
              <Download className="w-4 h-4 mr-2" />
              Get Chrome Extension
            </Button>
          </Link>
          <Link
            to="https://github.com/zh30/get-suno-lyric"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button variant="outline" size="lg">
              View Source Code
            </Button>
          </Link>
        </div>
      </div>

      {/* Demo Video */}
      <div className="flex justify-center mb-16">
        <iframe
          className="aspect-video w-full max-w-2xl rounded-lg shadow-xl"
          src="https://www.youtube.com/embed/_7x9GdWjreY?si=Fl8wxpOzuZRwnyqK"
          title="Suno Lyric Downloader Demo"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        />
      </div>

      {/* Key Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center mb-4">
              <Zap className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-lg">Automatic Detection</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Automatically detects Suno song pages and adds download buttons
              directly to the interface. No manual copying or pasting required.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center mb-4">
              <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-lg">Multiple Formats</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Download lyrics in LRC format for music players or SRT format for
              video subtitles. Easy format switching between downloads.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center mb-4">
              <Shield className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <CardTitle className="text-lg">Secure Authentication</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Uses your existing Suno session cookie for API access. No need to
              enter your credentials - completely secure and private.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-orange-100 dark:bg-orange-900/50 rounded-xl flex items-center justify-center mb-4">
              <Globe className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <CardTitle className="text-lg">Multi-language Support</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Available in English and Chinese. Extension interface adapts to your
              browser language preferences automatically.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center mb-4">
              <Music className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-lg">Precise Timing</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Downloads word-level synchronized lyrics with accurate timestamps
              down to the millisecond for perfect synchronization.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-teal-100 dark:bg-teal-900/50 rounded-xl flex items-center justify-center mb-4">
              <Lock className="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
            <CardTitle className="text-lg">Offline Operation</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              All processing happens locally in your browser. No external servers,
              no data collection, completely private and secure.
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      {/* How It Works */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-8 mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">How It Works</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-2xl font-bold text-blue-600 dark:text-blue-400">
              1
            </div>
            <h3 className="text-xl font-semibold">Navigate to Suno</h3>
            <p className="text-muted-foreground">
              Go to any Suno song page (https://suno.com/song/[song-id])
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center text-2xl font-bold text-green-600 dark:text-green-400">
              2
            </div>
            <h3 className="text-xl font-semibold">Download Buttons Appear</h3>
            <p className="text-muted-foreground">
              Extension automatically adds LRC and SRT download buttons on song
              cover images
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center text-2xl font-bold text-purple-600 dark:text-purple-400">
              3
            </div>
            <h3 className="text-xl font-semibold">Click & Download</h3>
            <p className="text-muted-foreground">
              Choose your format and download perfectly synchronized lyrics
              instantly
            </p>
          </div>
        </div>
      </div>

      {/* Supported Formats */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Supported Formats
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-blue-600" />
                LRC Format
              </CardTitle>
              <CardDescription>
                Perfect for music players and karaoke applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm mb-4">
                <div>[00:12.34]Hello world</div>
                <div>[00:15.67]This is synchronized lyrics</div>
                <div>[00:18.90]Perfect timing every time</div>
              </div>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Standard format supported by most music players</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Word-level timing with millisecond precision</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Ideal for karaoke and lyric displays</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-green-600" />
                SRT Format
              </CardTitle>
              <CardDescription>
                Ideal for video subtitles and captioning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm mb-4">
                <div>1</div>
                <div>00:00:12,340 --&gt; 00:00:15,670</div>
                <div>Hello world</div>
                <div className="mt-2">2</div>
                <div>00:00:15,670 --&gt; 00:00:19,120</div>
                <div>This is synchronized lyrics</div>
              </div>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Standard subtitle format for video players</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Compatible with YouTube, VLC, and media players</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                  <span>Perfect for creating lyric videos</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Technical Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Privacy & Security
            </CardTitle>
            <CardDescription>
              <Link
                to="/products/suno-lyric-downloader/privacy"
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                View detailed Privacy Policy →
              </Link>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Offline Operation:</strong> All processing happens
                  locally in your browser
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>No Data Collection:</strong> We don't collect any
                  personally identifiable information
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Secure Authentication:</strong> Uses your existing
                  Suno session, no credentials required
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Minimal Permissions:</strong> Only accesses Suno.com
                  domains
                </span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Technical Excellence
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Manifest V3:</strong> Built with latest Chrome extension
                  standards
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>TypeScript:</strong> Type-safe development with better
                  reliability
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Rspack:</strong> Fast builds and optimal bundling for
                  performance
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Chrome i18n:</strong> Full internationalization support
                </span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Installation */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-8 mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Installation</h2>

        <div className="max-w-2xl mx-auto space-y-6">
          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center text-sm font-semibold text-green-600 dark:text-green-400 mt-1">
              1
            </div>
            <div>
              <h3 className="font-semibold mb-2">Get from Chrome Web Store</h3>
              <p className="text-muted-foreground mb-3">
                Install directly from the Chrome Web Store for automatic updates
              </p>
              <Link
                to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm"
                className="cursor-pointer"
                target="_blank"
                rel="noreferrer"
              >
                <Button className="cursor-pointer">
                  <Download className="w-4 h-4 mr-2" />
                  Install from Chrome Store
                </Button>
              </Link>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold text-blue-600 dark:text-blue-400 mt-1">
              2
            </div>
            <div>
              <h3 className="font-semibold mb-2">Or Build from Source</h3>
              <p className="text-muted-foreground mb-3">
                For developers who want to customize or contribute
              </p>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm space-y-1">
                <div>git clone https://github.com/zh30/get-suno-lyric.git</div>
                <div>cd get-suno-lyric</div>
                <div>pnpm install</div>
                <div>pnpm build</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4">
          Ready to Download Perfectly Synchronized Lyrics?
        </h2>
        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          Join thousands of Suno users who are already enjoying perfectly timed
          lyrics for their favorite songs.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="https://chromewebstore.google.com/detail/suno-lyric-downloader/hhplbhnaldbldkgfkcfjklfneggokijm"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button className="cursor-pointer" size="lg">
              <Download className="w-4 h-4 mr-2" />
              Download Suno Lyric Downloader
            </Button>
          </Link>
          <Link
            to="https://github.com/zh30/get-suno-lyric"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button variant="outline" size="lg">
              <FileText className="w-4 h-4 mr-2" />
              Read Documentation
            </Button>
          </Link>
        </div>

        <div className="mt-6 text-center">
          <Link
            to="/products/suno-lyric-downloader/privacy"
            className="text-sm text-muted-foreground hover:text-blue-600 transition-colors"
          >
            View our Privacy Policy →
          </Link>
        </div>
      </div>
    </div>
  )
}
