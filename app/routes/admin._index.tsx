import { PageHeader } from "~/components/page-layout"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/admin._index"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "Admin Dashboard - <PERSON>",
    description: "Administrative dashboard for content management.",
  })
}

export async function loader(_: Route.LoaderArgs) {
  // Future: Load admin statistics and recent activity
  return { stats: {} }
}

export default function AdminIndex(_: Route.ComponentProps) {
  return (
    <PageHeader title="Admin" description="Dashboard for content management" />
  )
}
