import { LoginForm } from "~/components/login-form"
import { createMeta } from "~/lib/route-utils"
import type { Route } from "./+types/login"

export function meta(_: Route.MetaArgs) {
  return createMeta({
    title: "<PERSON><PERSON> - <PERSON>",
    description: "Sign in to access your account and manage your preferences.",
    ogUrl: "https://zhanghe.dev/login",
  })
}

export async function loader(_: Route.LoaderArgs) {
  return {
    message: "Login page loaded successfully",
  }
}

export default function Login(_: Route.ComponentProps) {
  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <LoginForm />
    </div>
  )
}
