import {
  BookOpen,
  Download,
  Eye,
  FileText,
  Globe,
  Keyboard,
  Lock,
  PanelLeft,
  Shield,
  Smartphone,
  Zap,
} from "lucide-react"
import { <PERSON> } from "react-router"
import { <PERSON><PERSON> } from "~/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card"
import { createMeta } from "~/lib/route-utils"

export function meta() {
  return createMeta({
    title:
      "Native Translate — Private, Built-in AI Translation Chrome Extension",
    description:
      "Privacy-first Chrome extension using Chrome's built-in AI Translator & Language Detector. No cloud calls, no telemetry. Local models with 25+ languages, hover-to-translate, full-page translation, and offline support. Open-source and completely free.",
    keywords:
      "Native Translate, Chrome extension, on-device translation, AI translation, language detection, privacy-first, offline, open-source, hover-to-translate, full-page translation, Chrome AI translator, local translation, privacy protection, language tools, translation software, browser extension, multilingual, automatic translation",
    ogImage: "https://assets.zhanghe.dev/native-translate/og.jpg",
    ogUrl: "https://zhanghe.dev/products/native-translate",
  })
}

export default function NativeTranslate() {
  return (
    <div className="container max-w-screen-xl mx-auto px-6 py-10 md:pt-20">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Globe className="w-8 h-8 text-blue-600" />
          <h1
            className="text-3xl md:text-5xl font-bold"
            style={{
              viewTransitionName: "blog-title",
            }}
          >
            Native Translate
          </h1>
        </div>

        {/* Breadcrumb navigation for SEO */}
        <nav
          aria-label="Breadcrumb navigation"
          className="text-sm opacity-60 mb-4"
        >
          <a href="/" className="hover:opacity-80">
            Home
          </a>
          <span className="mx-2">/</span>
          <a href="/products" className="hover:opacity-80">
            Products
          </a>
          <span className="mx-2">/</span>
          <span>Native Translate</span>
        </nav>

        <p className="text-xl md:text-2xl text-muted-foreground font-light mb-8 max-w-4xl mx-auto">
          Private, local‑first translation using Chrome's built‑in AI Translator
          &amp; Language Detector. No cloud calls, no telemetry — your content
          never leaves the browser.
        </p>

        <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground mb-8">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            <span>Privacy-first</span>
          </div>
          <div className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            <span>Open-source</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            <span>Lightweight</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="https://chromewebstore.google.com/detail/npnbioleceelkeepkobjfagfchljkphb"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button className="cursor-pointer" size="lg">
              <Download className="w-4 h-4 mr-2" />
              Get it for Free
            </Button>
          </Link>
          <Link
            to="https://github.com/zh30/native-translate"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button variant="outline" size="lg">
              View Source Code
            </Button>
          </Link>
        </div>
      </div>

      {/* Key Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center mb-4">
              <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-lg">Full-page Translation</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Preserves original layout by appending translated text under
              original blocks. Perfect for reading articles and documents in
              foreign languages.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center mb-4">
              <Eye className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-lg">Hover-to-translate</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Hold a modifier key (Alt/Control/Shift) and hover over paragraphs
              for instant translation. Translate only what you need, when you
              need it.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center mb-4">
              <Keyboard className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <CardTitle className="text-lg">Triple-space Translation</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              In text fields and contenteditable areas, type three spaces to
              translate typed content. Perfect for multilingual communication.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-orange-100 dark:bg-orange-900/50 rounded-xl flex items-center justify-center mb-4">
              <PanelLeft className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <CardTitle className="text-lg">Side Panel Translation</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Free-text translation with auto-detection. Prefers local APIs,
              falls back gracefully. Real-time translation as you type.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center mb-4">
              <BookOpen className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-lg">EPUB File Translation</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Upload and translate EPUB files with progress tracking and
              automatic download. Perfect for reading foreign language books.
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <div className="w-12 h-12 mx-auto bg-teal-100 dark:bg-teal-900/50 rounded-xl flex items-center justify-center mb-4">
              <Lock className="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
            <CardTitle className="text-lg">Privacy by Design</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Zero external translation requests by default. All processing
              happens locally in your browser with no telemetry or tracking.
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      {/* Core Benefits Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-8 mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Why Choose Native Translate?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-600" />
              Complete Privacy
            </h3>
            <p className="text-muted-foreground">
              Unlike cloud-based translation services, Native Translate
              processes everything locally using Chrome's built-in AI models.
              Your sensitive documents, private messages, and browsing data
              never leave your device.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Zap className="w-5 h-5 text-green-600" />
              Lightning Fast
            </h3>
            <p className="text-muted-foreground">
              With intelligent caching and local model processing, translations
              happen instantly. Models are cached per language pair, making
              subsequent translations even faster.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Globe className="w-5 h-5 text-purple-600" />
              25+ Languages Supported
            </h3>
            <p className="text-muted-foreground">
              Support for major languages including English, Chinese, Japanese,
              Korean, French, German, Spanish, Italian, Portuguese, Russian,
              Arabic, Hindi, and more.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Download className="w-5 h-5 text-orange-600" />
              Open Source & Free
            </h3>
            <p className="text-muted-foreground">
              Completely free and open-source under MIT license. No
              subscriptions, no premium features, no hidden costs. Just
              powerful, private translation for everyone.
            </p>
          </div>
        </div>
      </div>

      {/* Advanced Features */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Advanced Features
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <h3 className="text-xl font-semibold">
              Smart Translation Technology
            </h3>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Streaming Translation:</strong> Real-time progressive
                  translation for longer texts with visual feedback
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Smart Element Selection:</strong> Intelligently avoids
                  code blocks, tables, and navigation elements
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Multi-frame Support:</strong> Works in all frames
                  including about:blank pages
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>IME Awareness:</strong> Handles Asian language
                  composition events correctly
                </span>
              </li>
            </ul>
          </div>

          <div className="space-y-6">
            <h3 className="text-xl font-semibold">Technical Excellence</h3>
            <ul className="space-y-3 text-muted-foreground">
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Advanced Caching:</strong> Per-line and
                  per-language-pair caching with model readiness tracking
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>RTL/LTR Support:</strong> Automatic text direction and
                  alignment for target languages
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Bridge Architecture:</strong> Falls back to page-world
                  bridge when content script APIs are unavailable
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0" />
                <span>
                  <strong>Internationalized UI:</strong> Support for 25+
                  languages via Chrome i18n
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Supported Languages */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-8 mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">
          Supported Languages
        </h2>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
          {[
            "English",
            "Chinese (Simplified)",
            "Chinese (Traditional)",
            "Japanese",
            "Korean",
            "French",
            "German",
            "Spanish",
            "Italian",
            "Portuguese",
            "Russian",
            "Arabic",
            "Hindi",
            "Bengali",
            "Indonesian",
            "Turkish",
            "Vietnamese",
            "Thai",
            "Dutch",
            "Polish",
            "Persian",
            "Urdu",
            "Ukrainian",
            "Swedish",
            "Filipino",
          ].map((language) => (
            <div key={language} className="flex items-center gap-2 text-sm">
              <Globe className="w-3 h-3 text-green-600" />
              {language}
            </div>
          ))}
        </div>

        <p className="text-center text-muted-foreground mt-6">
          Plus auto-detection support for intelligent language identification
        </p>
      </div>

      {/* Requirements & Installation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Requirements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full" />
              <span>
                Chrome 138+ (Manifest V3, Side Panel APIs, Built-in AI)
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-blue-600 rounded-full" />
              <span>pnpm 9.15.1+ (for development)</span>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              Note: On first use, Chrome may download on-device models.
              Availability depends on device capability and Chrome's AI feature
              rollout.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="w-5 h-5" />
              Quick Installation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold">
                1
              </div>
              <span>
                Install dependencies:{" "}
                <code className="bg-muted px-2 py-1 rounded">pnpm install</code>
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold">
                2
              </div>
              <span>
                Development:{" "}
                <code className="bg-muted px-2 py-1 rounded">pnpm dev</code>
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center text-sm font-semibold">
                3
              </div>
              <span>Load unpacked extension in Chrome</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Privacy & Security */}
      <Card className="mb-16">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="w-5 h-5" />
            Privacy & Security
          </CardTitle>
          <CardDescription>
            <Link
              to="/products/native-translate/privacy"
              className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
            >
              View detailed Privacy Policy →
            </Link>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold mb-3">Privacy Commitment</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  • No analytics, no tracking, no cloud translation by default
                </li>
                <li>• All logic runs inside the browser</li>
                <li>• Works offline after models are downloaded and cached</li>
                <li>• Zero data collection or telemetry</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-3">Minimal Permissions</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>
                  • <code className="bg-muted px-1 rounded">storage</code> —
                  persist settings and readiness metadata
                </li>
                <li>
                  •{" "}
                  <code className="bg-muted px-1 rounded">activeTab, tabs</code>{" "}
                  — interact with the current tab
                </li>
                <li>
                  • <code className="bg-muted px-1 rounded">scripting</code> —
                  inject content script
                </li>
                <li>
                  • <code className="bg-muted px-1 rounded">sidePanel</code> —
                  optional side panel entry
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CTA Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4">
          Ready to Experience Private Translation?
        </h2>
        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          Join thousands of users who are already enjoying fast, private, and
          intelligent translation without compromising their privacy.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="https://chromewebstore.google.com/detail/npnbioleceelkeepkobjfagfchljkphb"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button className="cursor-pointer" size="lg">
              <Download className="w-4 h-4 mr-2" />
              Download Native Translate
            </Button>
          </Link>
          <Link
            to="https://github.com/zh30/native-translate"
            className="cursor-pointer"
            target="_blank"
            rel="noreferrer"
          >
            <Button variant="outline" size="lg">
              <FileText className="w-4 h-4 mr-2" />
              Read Documentation
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
