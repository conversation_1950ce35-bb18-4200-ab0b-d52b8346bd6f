import { desc } from "drizzle-orm"
import * as schema from "~/database/schema"
import { createSafeLoader } from "./route-utils"

/**
 * Common database queries with error handling
 */
export class DatabaseQueries {
  /**
   * Get all posts ordered by creation date
   */
  static async getAllPosts(db: any) {
    return createSafeLoader(
      () =>
        db.query.posts.findMany({
          orderBy: [desc(schema.posts.created_at)],
        }),
      [],
      "Failed to load posts"
    )
  }

  /**
   * Get post by ID
   */
  static async getPostById(db: any, id: number) {
    return createSafeLoader(
      () =>
        db.query.posts.findFirst({
          where: (posts: any, { eq }: any) => eq(posts.id, id),
        }),
      null,
      `Failed to load post with ID ${id}`
    )
  }

  /**
   * Get all products (placeholder for future database integration)
   */
  static async getAllProducts(db: any) {
    return createSafeLoader(
      () =>
        db.query.products?.findMany({
          orderBy: [desc(schema.products?.created_at)],
        }),
      [],
      "Failed to load products"
    )
  }
}

/**
 * Database error handling utility
 */
export function handleDatabaseError(error: unknown, defaultValue: any = null) {
  console.error("Database error:", error)
  return defaultValue
}
