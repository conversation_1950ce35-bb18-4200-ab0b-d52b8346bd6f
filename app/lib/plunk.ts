/**
 * 封装一个 plunk api fetch class
 * const options = {
  method: 'POST',
  headers: {'Content-Type': '<content-type>', Authorization: 'Bearer <token>'},
  body: '{"to":"<string>","subject":"<string>","body":"<string>","subscribed":true,"name":"<string>","from":"<string>","reply":"<string>","headers":{}}'
};

fetch('https://api.useplunk.com/v1/send', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  response:
  {
  "success": true,
  "emails": [
    {
      "contact": {
        "id": "80d74d13-16eb-48c5-bc2b-aae6fd5865cc",
        "email": "<EMAIL>"
      },
      "email": "ebd44b76-9e1d-4826-96a1-9a240c42a939"
    },
    {
      "contact": {
        "id": "8f33fe24a-1ce7-4498-8769-f80d67b4e59d",
        "email":  "<EMAIL>"
      },
      "email": "bd2d0ece-7a4e-48b8-a8be-be5af077adc1"
    }
  ],
  "timestamp": "1970-01-01T00:00:00.000Z"
}
 */

interface SendEmailOptions {
  to: string
  subject: string
  body: string
  from?: string
  reply?: string
  headers?: Record<string, string>
}

interface SendEmailResponse {
  success: boolean
  emails: {
    contact: {
      id: string
      email: string
    }[]
    email: string
  }[]
  timestamp: string
}

class Plunk {
  constructor(private apiKey: string) {
    this.apiKey = apiKey
  }

  async sendEmail({
    to,
    subject,
    body,
    from,
    reply,
    headers,
  }: SendEmailOptions): Promise<SendEmailResponse> {
    const response = await fetch(`https://api.useplunk.com/v1/send`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({ to, subject, body, from, reply, headers }),
    }).then((response) => response.json<SendEmailResponse>())

    return response
  }
}

export default Plunk
