{"$schema": "node_modules/wrangler/config-schema.json", "name": "zhang<PERSON>-dev", "compatibility_date": "2025-04-04", "main": "./workers/app.ts", "compatibility_flags": ["nodejs_compat"], "vars": {}, "d1_databases": [{"binding": "DB", "database_name": "zhangheblog", "database_id": "af33cc7b-139f-49e4-89fc-7a211804db7a", "migrations_dir": "drizzle"}], "r2_buckets": [{"binding": "R2_BUCKET", "bucket_name": "<PERSON><PERSON><PERSON><PERSON>"}], "routes": [{"pattern": "zhanghe.dev", "zone_name": "zhanghe.dev", "custom_domain": true}]}