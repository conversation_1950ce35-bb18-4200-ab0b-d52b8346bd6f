{"version": "6", "dialect": "sqlite", "id": "4f80cdc1-1abf-4f1e-8561-af28181460d8", "prevId": "008ee181-36ed-4cc3-b593-481f13e2254a", "tables": {"posts": {"name": "posts", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "private": {"name": "private", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "author_id": {"name": "author_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "telegram_message_id": {"name": "telegram_message_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_url": {"name": "cover_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"posts_author_id_users_id_fk": {"name": "posts_author_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_admin": {"name": "is_admin", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "challenge": {"name": "challenge", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "credentials": {"name": "credentials", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}