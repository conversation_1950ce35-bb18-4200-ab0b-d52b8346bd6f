{"version": "6", "dialect": "sqlite", "id": "008ee181-36ed-4cc3-b593-481f13e2254a", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"guestBook": {"name": "<PERSON><PERSON><PERSON>", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"guestBook_email_unique": {"name": "guestBook_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}