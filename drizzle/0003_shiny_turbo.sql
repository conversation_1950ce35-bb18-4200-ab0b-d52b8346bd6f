CREATE TABLE `posts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text NOT NULL,
	`content` text NOT NULL,
	`private` integer DEFAULT 0 NOT NULL,
	`author_id` integer,
	`tags` text,
	`slug` text,
	`telegram_message_id` integer,
	`cover_url` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	`excerpt` text,
	FOREIGN KEY (`author_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`title` text NOT NULL,
	`description` text NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text,
	`email` text NOT NULL,
	`password` text NOT NULL,
	`is_admin` integer DEFAULT 0,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	`challenge` text,
	`credentials` text
);
--> statement-breakpoint
DROP TABLE `guestBook`;