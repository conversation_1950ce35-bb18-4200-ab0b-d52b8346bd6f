{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "type": "module", "scripts": {"build": "react-router build", "cf-typegen": "wrangler types", "db:generate": "dotenv -- drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply --local DB", "db:migrate-production": "dotenv -e .dev.vars -- drizzle-kit migrate", "deploy": "pnpm run build && wrangler deploy", "dev": "react-router dev", "start": "wrangler dev", "preview": "pnpm run build && vite preview", "typecheck": "pnpm run cf-typegen && react-router typegen && tsc -b", "format": "biome format --write .", "format:check": "biome format .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "check": "biome check .", "check:fix": "biome check --write ."}, "dependencies": {"@farcaster/frame-sdk": "^0.0.34", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.33", "@react-email/render": "^1.1.4", "@react-router/fs-routes": "^7.7.1", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "@tailwindcss/typography": "^0.5.16", "@types/html2canvas": "^1.0.0", "@zumer/snapdom": "^1.9.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "~0.36.4", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "input-otp": "^1.4.2", "isbot": "^5.1.29", "lucide-react": "^0.479.0", "marked": "^15.0.12", "motion": "^12.23.12", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "9.7.0", "react-dom": "^19.1.1", "react-ga4": "^2.1.0", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "react-router": "^7.7.1", "recharts": "^2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@cloudflare/vite-plugin": "^1.11.1", "@cloudflare/workers-types": "^4.20250805.0", "@iconify-json/fluent-emoji": "^1.2.3", "@iconify-json/ri": "^1.2.5", "@iconify-json/simple-icons": "^1.2.46", "@iconify/tailwind4": "^1.0.6", "@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "dotenv-cli": "^7.4.4", "drizzle-kit": "~0.28.1", "react-email": "4.0.13", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.28.0"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "sharp", "workerd"]}}