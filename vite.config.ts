import { cloudflare } from "@cloudflare/vite-plugin"
import { reactRouter } from "@react-router/dev/vite"
import tailwindcss from "@tailwindcss/vite"
import { defineConfig } from "vite"
import tsconfigPaths from "vite-tsconfig-paths"

export default defineConfig(({ isSsrBuild }) => ({
  server: {
    port: 3000,
    headers: {
      "Cross-Origin-Embedder-Policy": "require-corp",
      "Cross-Origin-Opener-Policy": "same-origin",
    },
  },
  plugins: [
    cloudflare({ viteEnvironment: { name: "ssr" } }),
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
  ],
  optimizeDeps: {
    exclude: ["@ffmpeg/ffmpeg", "@ffmpeg/util"],
    include: ["@ffmpeg/core"],
  },
  define: {
    global: "globalThis",
  },
  // Add resolve aliases to prevent duplicate React instances
  resolve: {
    dedupe: ["react", "react-dom"],
  },
  // Suppress chunk size warnings for large game assets
  build: {
    chunkSizeWarningLimit: 1000,
  },
}))
