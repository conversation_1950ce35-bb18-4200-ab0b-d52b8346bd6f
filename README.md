# 张赫的个人网站 (zhanghe.dev)

基于 React Router 7 和 Cloudflare Workers 构建的现代全栈 Web 应用，集成了博客、产品展示和管理功能。

## ✨ 主要功能

- **📝 博客系统** - 支持 Markdown 的博客文章发布和管理
- **🛠️ 产品展示** - 多个在线工具和产品页面，包括：
  - Video Clipper - 基于 FFmpeg.wasm 的在线视频剪辑工具
  - Suno Lyric Downloader - Suno 音乐平台歌词下载工具
  - Cover Moment - 专辑封面生成器
  - MangoFlow - 工作流管理工具
- **👤 用户系统** - 登录认证和管理员后台
- **📧 邮件集成** - 基于 React Email 的邮件模板系统

## 🚀 技术栈

### 核心框架
- **Frontend**: React 19 + React Router 7 (全栈框架)
- **Backend**: Cloudflare Workers (边缘计算)
- **Build Tool**: Vite + Cloudflare Workers 集成
- **Package Manager**: pnpm 10.13.1

### 数据库与存储
- **数据库**: Cloudflare D1 (SQLite) 
- **ORM**: Drizzle ORM (类型安全的数据库操作)
- **文件存储**: Cloudflare R2 (对象存储)

### UI 与样式
- **CSS 框架**: Tailwind CSS v4
- **组件库**: shadcn/ui (基于 Radix UI primitives)
- **图标**: Lucide React + Iconify (fluent-emoji, ri, simple-icons)
- **主题**: next-themes (暗/亮模式切换)

### 开发工具
- **类型检查**: TypeScript (严格模式)
- **代码质量**: Biome 2.1.3 (格式化 + 代码检查)
- **表单**: react-hook-form + zod resolvers
- **日期处理**: date-fns + react-day-picker

### 特色功能库
- **视频处理**: FFmpeg.wasm (客户端视频处理)
- **动画**: GSAP + Motion
- **图表**: Recharts
- **轮播**: Embla Carousel React  
- **通知**: Sonner
- **邮件**: @react-email/components

## 🏗️ 项目结构

```
app/                      # React Router 应用主目录
├── routes/               # 文件路由 (自动生成)
│   ├── _index.tsx        # 首页
│   ├── posts/            # 博客相关路由
│   ├── products/         # 产品页面路由
│   └── admin/            # 管理后台路由
├── components/           # 可复用组件
│   ├── ui/               # shadcn/ui 组件库
│   └── [business]        # 业务组件
├── lib/                  # 工具函数与业务逻辑
├── hooks/                # 自定义 React Hooks
└── routes.ts             # 路由配置

database/                 # 数据库架构定义
├── schema.ts             # Drizzle ORM 架构 (users, posts, products)

workers/                  # Cloudflare Workers 入口
└── app.ts                # 主 Worker 处理器

drizzle/                  # 数据库迁移文件 (自动生成)
public/                   # 静态资源
```

## 🛠️ 开发命令

### 环境设置
```bash
# 安装依赖
pnpm install

# 初始化数据库
pnpm db:migrate
```

### 开发调试
```bash
# 启动开发服务器 (端口 5173)
pnpm dev

# 本地生产环境测试
pnpm start
```

### 数据库管理
```bash
# 从架构变更生成迁移文件
pnpm db:generate

# 应用迁移到本地 D1
pnpm db:migrate

# 应用迁移到生产 D1
pnpm db:migrate-production
```

### 代码质量
```bash
# TypeScript 类型检查
pnpm typecheck

# 代码格式化
pnpm format

# 代码检查
pnpm lint

# 修复格式和检查问题
pnpm check:fix
```

### 构建与部署
```bash
# 构建生产版本
pnpm build

# 部署到 Cloudflare
pnpm deploy

# 上传预览版本
npx wrangler versions upload

# 发布到生产环境
npx wrangler versions deploy
```

## 🌐 部署架构

### Cloudflare 集成
- **域名**: zhanghe.dev (自定义域名)
- **计算**: Cloudflare Workers (边缘计算)
- **存储**: D1 数据库 + R2 对象存储
- **CDN**: Cloudflare Pages (静态资源)

### 环境配置
生产环境需要的环境变量：
- `CLOUDFLARE_ACCOUNT_ID` - Cloudflare 账户 ID
- `CLOUDFLARE_TOKEN` - Cloudflare API Token
- `GA_TRACKING_ID` - Google Analytics 跟踪 ID

## 📁 核心路由

- **首页**: `/` - 项目介绍和导航
- **博客**: `/posts` - 文章列表和详情页
- **产品**: `/products` - 工具和产品展示
- **管理**: `/admin` - 后台管理界面
- **API**: `/api/*` - RESTful API 端点

## 🎨 UI 设计

### 组件系统
- **设计系统**: 基于 Radix UI 的无障碍组件库
- **样式方案**: Tailwind CSS 实用类 + CSS 变量主题
- **响应式**: 移动优先的响应式设计
- **交互**: GSAP 和 Motion 驱动的流畅动画

### 主题支持
- 🌙 暗色模式 / ☀️ 亮色模式
- 🎨 CSS 变量驱动的主题系统
- 📱 跨设备一致的视觉体验

## 🔧 架构特点

### 全栈 React Router 7
- **SSR**: 服务端渲染支持
- **文件路由**: 基于文件系统的自动路由
- **类型安全**: 自动生成的路由类型定义
- **数据加载**: Loaders 和 Actions 处理数据

### 现代开发体验
- **热更新**: Vite HMR 快速开发
- **类型安全**: 端到端 TypeScript 支持
- **代码质量**: Biome 统一的代码标准
- **部署简单**: 一键部署到 Cloudflare

---

基于 React Router 7 + Cloudflare Workers 构建 | 托管于 [zhanghe.dev](https://zhanghe.dev)