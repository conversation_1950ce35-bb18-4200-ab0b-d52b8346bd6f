# AGENTS

- Build/dev: `pnpm dev` (HMR), `pnpm build`, `pnpm preview`, `pnpm start` (Wrangler dev), deploy with `pnpm deploy`
- Type/lint/format: `pnpm typecheck` (wrangler types + RR typegen + tsc -b), `pnpm lint`, `pnpm lint:fix`, `pnpm format`, `pnpm format:check`, `pnpm check`, `pnpm check:fix`
- DB: `pnpm db:generate` after schema changes, `pnpm db:migrate` (local), `pnpm db:migrate-production` (prod)
- Tests: no test runner configured; if adding tests, prefer Vitest; single test: `vitest run path/to/file.test.ts --pool=threads --reporter=default`
- Package manager: pnpm@10.13.1; do not use npm/yarn. Node via .nvmrc. Do not modify git config.
- Imports: React first, external libs, then local `~/` alias; use `import type` for types; keep groups ordered and sorted; avoid default-then-named mixing if unnecessary.
- Formatting: Biome 2.1.3 is source of truth; 2-space indent, double quotes, semicolons as needed; run format/lint before commits/PRs.
- Types: strict TS; no `any` (use `unknown`/generics); type all props/returns; infer DB types from Drizzle schema; route types via React Router 7 typegen.
- Naming: Components PascalCase, hooks camelCase with `use` prefix, utils camelCase; files follow these; routes use flat dot notation (e.g., `products.video-clipper.tsx`).
- Error handling: wrap async with try/catch; surface typed errors; avoid swallowing errors; for UI, use toasts (Sonner) and meaningful fallbacks.
- React patterns: functional components, hooks, memoization when needed; fragments over extra divs; client-only code in effects; theming via next-themes.
- Data flow: use loaders/actions for SSR; fetch via built-in fetch; DB via Drizzle with D1 context from workers/app.ts.
- Styling: Tailwind v4 utilities; prefer `clsx` + `tailwind-merge`; keep class strings readable; leverage shadcn/ui primitives in app/components/ui.
- Cursor rules: follow .cursor/rules/frontend-development-rules.mdc (TypeScript-first, pnpm, Vite, Tailwind, import ordering, performance, avoid any/over-engineering).
- Cloudflare: generate worker types with `pnpm cf-typegen`; use wrangler for local/prod; respect nodejs_compat and D1/R2 bindings in wrangler.jsonc.
- Icons/images: use Lucide/Iconify collections in package.json; keep assets in public/.
- Security: never commit secrets; use env via Wrangler; do not log tokens.
- Commit/PRs: ensure `pnpm typecheck && pnpm check` pass; write concise messages; prefer small, focused changes.
