{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.json", "**/*.css"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useImportType": "warn"}, "correctness": {"useExhaustiveDependencies": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded", "trailingCommas": "es5"}}, "json": {"formatter": {"indentStyle": "space", "indentWidth": 2}}, "css": {"formatter": {"indentStyle": "space", "indentWidth": 2}}, "overrides": [{"includes": ["build/**", "dist/**", "node_modules/**", "*.min.js", "*.min.css", "public/**", "coverage/**", ".next/**", "drizzle/**", "*.d.ts"], "formatter": {"enabled": false}, "linter": {"enabled": false}}]}