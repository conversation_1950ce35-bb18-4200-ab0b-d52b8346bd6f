import { sql } from "drizzle-orm"
import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core"

export const users = sqliteTable("users", {
  id: integer().primaryKey({ autoIncrement: true }),
  name: text(),
  email: text().notNull(),
  password: text().notNull(),
  is_admin: integer().default(0),
  created_at: text().default(sql`CURRENT_TIMESTAMP`),
  challenge: text(),
  credentials: text(),
})

export const posts = sqliteTable("posts", {
  id: integer().primaryKey({ autoIncrement: true }),
  title: text().notNull(),
  content: text().notNull(),
  private: integer().notNull().default(0),
  author_id: integer().references(() => users.id),
  tags: text(),
  slug: text(),
  telegram_message_id: integer(),
  cover_url: text(),
  created_at: text().default(sql`CURRENT_TIMESTAMP`),
  excerpt: text(),
})

export const products = sqliteTable("products", {
  id: integer().primary<PERSON>ey({ autoIncrement: true }),
  title: text().notNull(),
  description: text().notNull(),
  created_at: text().default(sql`CURRENT_TIMESTAMP`),
})

export type Post = typeof posts.$inferSelect

export type User = typeof users.$inferSelect

export type Product = typeof products.$inferSelect
